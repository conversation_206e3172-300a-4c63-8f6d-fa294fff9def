<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BioMed LMS - Site Map</title>
  <link rel="icon" type="image/svg+xml" href="/public/favicon.svg">
  <script src="https://cdn.tailwindcss.com"></script>
  
  <style>
    .site-link {
      transition: all 0.3s ease;
    }
    .site-link:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.1);
    }
    .category-section {
      transition: all 0.2s ease;
    }
    .category-section:hover {
      background-color: rgba(59, 130, 246, 0.05);
    }
  </style>
</head>
<body class="bg-gray-50">
  <!-- Navigation -->
  <header class="bg-blue-700 text-white shadow-lg">
    <nav class="container mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <a href="navigation-index.html" class="text-2xl font-bold hover:text-blue-200 transition-colors flex items-center space-x-2">
            <span class="text-3xl">🏥</span>
            <span>BioMed LMS</span>
          </a>
          <span class="text-blue-300 text-sm">/ Site Map</span>
        </div>
        <div class="flex items-center space-x-4">
          <a href="navigation-index.html" class="bg-blue-600 hover:bg-blue-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            Navigation Hub
          </a>
          <a href="index.html" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            React App
          </a>
        </div>
      </div>
    </nav>
  </header>

  <!-- Site Map Content -->
  <section class="py-16">
    <div class="container mx-auto px-6">
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">BioMed LMS Site Map</h1>
        <p class="text-lg text-gray-600 max-w-3xl mx-auto">
          Complete navigation guide to all pages and resources in the BioMed LMS platform
        </p>
      </div>

      <!-- Main Application Pages -->
      <div class="category-section bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <span class="text-3xl mr-3">🏠</span>
          Main Application Pages
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <a href="navigation-index.html" class="site-link block bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">Navigation Hub</h3>
            <p class="text-blue-700 text-sm mb-3">Central navigation dashboard with links to all platform sections</p>
            <span class="text-blue-600 text-xs font-medium">navigation-index.html</span>
          </a>
          
          <a href="BioMed LMS.html" class="site-link block bg-teal-50 p-6 rounded-lg border border-teal-200">
            <h3 class="text-lg font-semibold text-teal-900 mb-2">Original Landing Page</h3>
            <p class="text-teal-700 text-sm mb-3">Original BioMed LMS landing page with course divisions</p>
            <span class="text-teal-600 text-xs font-medium">BioMed LMS.html</span>
          </a>
          
          <a href="preview.html" class="site-link block bg-gray-50 p-6 rounded-lg border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Homepage Preview</h3>
            <p class="text-gray-700 text-sm mb-3">Basic homepage with hero section and features showcase</p>
            <span class="text-gray-600 text-xs font-medium">preview.html</span>
          </a>
          
          <a href="enhanced-homepage-preview.html" class="site-link block bg-purple-50 p-6 rounded-lg border border-purple-200">
            <h3 class="text-lg font-semibold text-purple-900 mb-2">Enhanced Homepage</h3>
            <p class="text-purple-700 text-sm mb-3">Advanced homepage with interactive specialization selection</p>
            <span class="text-purple-600 text-xs font-medium">enhanced-homepage-preview.html</span>
          </a>
          
          <a href="subject-categories-preview.html" class="site-link block bg-green-50 p-6 rounded-lg border border-green-200">
            <h3 class="text-lg font-semibold text-green-900 mb-2">Subject Categories</h3>
            <p class="text-green-700 text-sm mb-3">Three biomedical engineering divisions with course modules</p>
            <span class="text-green-600 text-xs font-medium">subject-categories-preview.html</span>
          </a>
          
          <a href="enhanced-course-details-preview.html" class="site-link block bg-red-50 p-6 rounded-lg border border-red-200">
            <h3 class="text-lg font-semibold text-red-900 mb-2">Course Details</h3>
            <p class="text-red-700 text-sm mb-3">Comprehensive course module breakdown with learning outcomes</p>
            <span class="text-red-600 text-xs font-medium">enhanced-course-details-preview.html</span>
          </a>
        </div>
      </div>

      <!-- React Application -->
      <div class="category-section bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <span class="text-3xl mr-3">⚛️</span>
          React Application
        </h2>
        <div class="grid md:grid-cols-2 gap-6">
          <a href="index.html" class="site-link block bg-green-50 p-6 rounded-lg border border-green-200">
            <h3 class="text-lg font-semibold text-green-900 mb-2">React App Entry Point</h3>
            <p class="text-green-700 text-sm mb-3">Full-featured interactive application with routing and state management</p>
            <span class="text-green-600 text-xs font-medium">index.html</span>
          </a>
          
          <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">React Components</h3>
            <p class="text-gray-700 text-sm mb-3">TypeScript React components in src/ directory</p>
            <ul class="text-gray-600 text-xs space-y-1">
              <li>• src/App.tsx - Main application</li>
              <li>• src/pages/ - Page components</li>
              <li>• src/components/ - Reusable components</li>
              <li>• src/services/ - API services</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Specialization Direct Links -->
      <div class="category-section bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <span class="text-3xl mr-3">🎓</span>
          Specialization Direct Links
        </h2>
        <div class="grid md:grid-cols-3 gap-6">
          <a href="subject-categories-preview.html?specialization=biomedical-imaging" class="site-link block bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">🔬 Biomedical Imaging</h3>
            <p class="text-blue-700 text-sm mb-3">6 courses covering MRI, CT, Ultrasound, Nuclear Medicine, Digital Radiography, and Image Processing</p>
            <span class="text-blue-600 text-xs font-medium">18-20 Credits</span>
          </a>
          
          <a href="subject-categories-preview.html?specialization=electrophysiological" class="site-link block bg-green-50 p-6 rounded-lg border border-green-200">
            <h3 class="text-lg font-semibold text-green-900 mb-2">⚡ Electrophysiological</h3>
            <p class="text-green-700 text-sm mb-3">6 courses covering ECG, EEG, EMG, Bioamplifiers, Signal Conditioning, and Neural Interfaces</p>
            <span class="text-green-600 text-xs font-medium">20-22 Credits</span>
          </a>
          
          <a href="subject-categories-preview.html?specialization=biomechanics-rehab" class="site-link block bg-purple-50 p-6 rounded-lg border border-purple-200">
            <h3 class="text-lg font-semibold text-purple-900 mb-2">🦴 Biomechanics & Rehab</h3>
            <p class="text-purple-700 text-sm mb-3">6 courses covering Gait Analysis, Prosthetics, Orthotics, Biomaterials, Robotics, and Modeling</p>
            <span class="text-purple-600 text-xs font-medium">22-24 Credits</span>
          </a>
        </div>
      </div>

      <!-- Documentation & Resources -->
      <div class="category-section bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <span class="text-3xl mr-3">📚</span>
          Documentation & Resources
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <a href="PROJECT_STRUCTURE.md" class="site-link block bg-yellow-50 p-6 rounded-lg border border-yellow-200">
            <h3 class="text-lg font-semibold text-yellow-900 mb-2">📋 Project Structure</h3>
            <p class="text-yellow-700 text-sm mb-3">Detailed folder organization and architecture guide</p>
            <span class="text-yellow-600 text-xs font-medium">PROJECT_STRUCTURE.md</span>
          </a>
          
          <a href="SETUP_COMPLETE.md" class="site-link block bg-green-50 p-6 rounded-lg border border-green-200">
            <h3 class="text-lg font-semibold text-green-900 mb-2">✅ Setup Complete</h3>
            <p class="text-green-700 text-sm mb-3">Implementation summary and features overview</p>
            <span class="text-green-600 text-xs font-medium">SETUP_COMPLETE.md</span>
          </a>
          
          <a href="assets/css/main.css" class="site-link block bg-pink-50 p-6 rounded-lg border border-pink-200">
            <h3 class="text-lg font-semibold text-pink-900 mb-2">🎨 CSS Assets</h3>
            <p class="text-pink-700 text-sm mb-3">Custom stylesheets and design system</p>
            <span class="text-pink-600 text-xs font-medium">assets/css/main.css</span>
          </a>
          
          <a href="assets/js/utils.js" class="site-link block bg-indigo-50 p-6 rounded-lg border border-indigo-200">
            <h3 class="text-lg font-semibold text-indigo-900 mb-2">⚙️ JS Utilities</h3>
            <p class="text-indigo-700 text-sm mb-3">JavaScript helper functions and utilities</p>
            <span class="text-indigo-600 text-xs font-medium">assets/js/utils.js</span>
          </a>
        </div>
      </div>

      <!-- Navigation Components -->
      <div class="category-section bg-white rounded-xl shadow-lg p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <span class="text-3xl mr-3">🧭</span>
          Navigation Components
        </h2>
        <div class="grid md:grid-cols-2 gap-6">
          <a href="shared-navigation.html" class="site-link block bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">Shared Navigation</h3>
            <p class="text-blue-700 text-sm mb-3">Reusable navigation component with dropdown menus and mobile support</p>
            <span class="text-blue-600 text-xs font-medium">shared-navigation.html</span>
          </a>
          
          <a href="sitemap.html" class="site-link block bg-gray-50 p-6 rounded-lg border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Site Map</h3>
            <p class="text-gray-700 text-sm mb-3">Complete overview of all pages and navigation structure (this page)</p>
            <span class="text-gray-600 text-xs font-medium">sitemap.html</span>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Quick Navigation Footer -->
  <div class="bg-blue-800 text-white py-4">
    <div class="container mx-auto px-6">
      <div class="flex items-center justify-between text-sm">
        <div class="flex items-center space-x-4">
          <span class="text-blue-300">Quick Access:</span>
          <a href="navigation-index.html" class="hover:text-blue-200 transition-colors">Hub</a>
          <a href="BioMed LMS.html" class="hover:text-blue-200 transition-colors">Original</a>
          <a href="subject-categories-preview.html" class="hover:text-blue-200 transition-colors">Categories</a>
          <a href="enhanced-course-details-preview.html" class="hover:text-blue-200 transition-colors">Details</a>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-blue-300">React:</span>
          <a href="index.html" class="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-xs transition-colors">Launch App</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-800 text-white py-8">
    <div class="container mx-auto px-6 text-center">
      <div class="flex items-center justify-center space-x-2 mb-4">
        <span class="text-2xl">🏥</span>
        <h5 class="text-lg font-semibold">BioMed LMS</h5>
      </div>
      <p class="text-gray-400 text-sm mb-4">
        Comprehensive biomedical learning management system with complete navigation structure
      </p>
      <p class="text-gray-500 text-xs">&copy; 2025 BioMed LMS. All rights reserved. | Site Map</p>
    </div>
  </footer>
</body>
</html>
