
import React, { useState, useEffect } from 'react';
import { fetchLMSInformation } from '../services/geminiService';
import { GeminiLMSData, LMSInfo, MarketPlayer } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import LMSCard from '../components/LMSCard';
import MarketPlayerCard from '../components/MarketPlayerCard';

const DiscoverLMSPage: React.FC = () => {
  const [lmsData, setLmsData] = useState<GeminiLMSData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await fetchLMSInformation();
        setLmsData(data);
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred.");
        }
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    loadData();
  }, []);

  if (isLoading) {
    return <LoadingSpinner message="Fetching LMS insights from AI..." />;
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-semibold text-red-600 mb-2">Error Fetching Data</h2>
        <p className="text-red-500 bg-red-100 p-4 rounded-md">{error}</p>
        <p className="mt-4 text-gray-600">Please ensure your API key is correctly configured and try again later.</p>
      </div>
    );
  }

  if (!lmsData || (!lmsData.recommendedLMS?.length && !lmsData.marketPlayers?.length)) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-semibold text-gray-700 mb-2">No Information Available</h2>
        <p className="text-gray-500">The AI could not retrieve information at this time. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      <section className="text-center py-8 bg-gradient-to-r from-primary-600 to-secondary-500 rounded-lg shadow-xl text-white">
        <h1 className="text-4xl font-bold mb-3">Discover Top LMS for Biomedical Engineering</h1>
        <p className="text-lg opacity-90 max-w-2xl mx-auto">
          AI-curated insights into leading Learning Management Systems and key market players in the field of Biomedical Engineering and Instrumentation.
        </p>
      </section>

      {lmsData.recommendedLMS && lmsData.recommendedLMS.length > 0 && (
        <section>
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 pb-2 border-b-2 border-primary-500">Recommended LMS Platforms</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {lmsData.recommendedLMS.map((lms: LMSInfo, index: number) => (
              <LMSCard key={index} lms={lms} />
            ))}
          </div>
        </section>
      )}

      {lmsData.marketPlayers && lmsData.marketPlayers.length > 0 && (
        <section>
          <h2 className="text-3xl font-semibold text-gray-800 mb-6 pb-2 border-b-2 border-secondary-500">Fast-Growing Startups & Market Leaders</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {lmsData.marketPlayers.map((player: MarketPlayer, index: number) => (
              <MarketPlayerCard key={index} player={player} />
            ))}
          </div>
        </section>
      )}
    </div>
  );
};

export default DiscoverLMSPage;
