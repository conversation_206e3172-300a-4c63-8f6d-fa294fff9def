
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { GEMINI_MODEL_NAME, GEMINI_API_KEY } from '../constants';
import { GeminiLMSData } from '../types';

if (!GEMINI_API_KEY) {
  console.error("Gemini API key is not set. Please set the API_KEY environment variable.");
}

const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY! });

const PROMPT = `
You are an expert research assistant specializing in educational technology for niche engineering fields.
Please provide a list of the most successful Learning Management Systems (LMS) specifically suited for Biomedical Engineering and Instrumentation education and training.
For each LMS, include:
1.  "name": The name of the LMS.
2.  "description": A brief description (1-2 sentences) highlighting its relevance to biomedical engineering or instrumentation.
3.  "keyFeatures": A list of 2-3 key features beneficial for this field (e.g., simulation integration, lab module support, specialized content libraries).
4.  "targetAudience": Typical users (e.g., universities, corporate training, vocational schools).

Additionally, identify 2-3 "fastGrowingStartupsOrMarketLeaders" in this specific LMS niche. For each, provide:
1.  "name": Name of the company/platform.
2.  "focus": Their specific focus or innovation in the biomedical engineering LMS space.

Return the entire response as a single JSON object with two main keys: "recommendedLMS" (an array of LMS objects) and "marketPlayers" (an array of startup/leader objects). Ensure the JSON is well-formed and valid.

Example structure for an LMS object:
{
  "name": "ExampleLMS Platform",
  "description": "An LMS designed for hands-on technical training with robust support for virtual labs.",
  "keyFeatures": ["Virtual lab integration", "Competency tracking", "Interactive 3D models"],
  "targetAudience": "Universities and specialized training centers"
}

Example structure for a market player object:
{
  "name": "InnovateBioEd Tech",
  "focus": "Developing AI-driven personalized learning paths for biomedical device training."
}
`;

export const fetchLMSInformation = async (): Promise<GeminiLMSData> => {
  if (!GEMINI_API_KEY) {
    throw new Error("Gemini API key is not configured.");
  }
  try {
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: GEMINI_MODEL_NAME,
      contents: PROMPT,
      config: {
        responseMimeType: "application/json",
        temperature: 0.3, // Lower temperature for more factual, less creative output
      },
    });

    let jsonStr = response.text.trim();
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsedData = JSON.parse(jsonStr) as GeminiLMSData;

    // Basic validation of the parsed data structure
    if (!parsedData.recommendedLMS || !parsedData.marketPlayers || 
        !Array.isArray(parsedData.recommendedLMS) || !Array.isArray(parsedData.marketPlayers)) {
      console.error("Parsed JSON does not match expected structure:", parsedData);
      throw new Error("Received malformed data structure from API.");
    }
    
    return parsedData;

  } catch (error) {
    console.error("Error fetching LMS information from Gemini API:", error);
    if (error instanceof Error) {
        throw new Error(`Failed to fetch LMS data: ${error.message}`);
    }
    throw new Error("An unknown error occurred while fetching LMS data.");
  }
};
