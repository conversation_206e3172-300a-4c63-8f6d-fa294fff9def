
import React from 'react';

interface DiagramCardProps {
  imageUrl: string;
  title: string;
  description?: string;
}

export const DiagramCard: React.FC<DiagramCardProps> = ({ imageUrl, title, description }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-md font-semibold text-blue-700 mb-3 text-center">{title}</h3>
      <img src={imageUrl} alt={title} className="w-full h-auto object-contain rounded-md mb-3 max-h-60" />
      {description && <p className="text-xs text-slate-600">{description}</p>}
    </div>
  );
};
