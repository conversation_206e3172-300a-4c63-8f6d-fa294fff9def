<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BioMed LMS – Advancing Biomedical Engineering Education</title>
    <meta name="description" content="An elite platform empowering biomedical engineering students and professionals through focused, innovative, and industry-ready learning pathways.">
    <meta name="keywords" content="Biomedical Engineering, LMS, Education, Training, Medical Devices, Imaging, Instrumentation, Electronics, Biomechanics, Rehabilitation">
    
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5'%3E%3C/path%3E%3C/svg%3E" type="image/svg+xml">

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth; /* Smooth scrolling for anchor links */
        }
        .hero-bg {
            background-image: url('https://placehold.co/1920x600/E0F2F7/000000?text=BioMed+LMS+Hero+Image');
            background-size: cover;
            background-position: center;
        }
        .card-hover-effect {
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        }
        .card-hover-effect:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        .social-icon {
            transition: transform 0.2s ease-in-out;
        }
        .social-icon:hover {
            transform: scale(1.2);
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <header class="bg-white shadow-sm py-4 fixed w-full z-10">
        <nav class="container mx-auto px-4 flex justify-between items-center">
            <a href="#" class="text-2xl font-bold text-teal-600">BioMed LMS</a>
            <ul class="flex space-x-6">
                <li><a href="#home" class="text-gray-600 hover:text-teal-600 font-medium transition-colors duration-200">Home</a></li>
                <li><a href="#divisions" class="text-gray-600 hover:text-teal-600 font-medium transition-colors duration-200">Courses</a></li>
                <li><a href="#contact" class="text-gray-600 hover:text-teal-600 font-medium transition-colors duration-200">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home" class="hero-bg relative text-white py-24 md:py-32 flex items-center justify-center min-h-screen-75">
            <div class="absolute inset-0 bg-gradient-to-r from-teal-700 to-blue-800 opacity-80"></div>
            <div class="container mx-auto px-4 text-center relative z-10">
                <h1 class="text-4xl md:text-6xl font-extrabold leading-tight mb-4 animate-fade-in-up">
                    BioMed LMS – Advancing Biomedical Engineering Education
                </h1>
                <p class="text-xl md:text-2xl max-w-3xl mx-auto mb-8 opacity-0 animate-fade-in-up animation-delay-500">
                    An elite platform empowering biomedical engineering students and professionals through focused, innovative, and industry-ready learning pathways.
                </p>
                <button id="exploreCoursesBtn" class="bg-white text-teal-700 hover:bg-teal-100 px-8 py-3 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-teal-300 opacity-0 animate-fade-in-up animation-delay-1000">
                    Explore Our Programs
                </button>
            </div>
        </section>

        <section id="divisions" class="py-16 md:py-24 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-bold text-center text-gray-800 mb-12">Our Specialized Learning Pathways</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="bg-blue-50 p-8 rounded-xl shadow-lg card-hover-effect flex flex-col items-center text-center">
                        <img src="https://placehold.co/100x100/A7D9EB/000000?text=Imaging" alt="Biomedical Imaging Icon" class="w-24 h-24 mb-6 rounded-full border-4 border-blue-200 p-2">
                        <h3 class="text-2xl font-semibold text-blue-800 mb-4">Biomedical Imaging & Instrumentation Technology</h3>
                        <p class="text-gray-700 leading-relaxed mb-6">
                            Dive deep into the core principles and advanced applications of modern medical imaging systems. Understand the physics and engineering behind MRI, CT, Ultrasound, and PET, and master the instrumentation technologies that bring these systems to life.
                        </p>
                        <a href="#" class="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">Learn More &rarr;</a>
                    </div>

                    <div class="bg-green-50 p-8 rounded-xl shadow-lg card-hover-effect flex flex-col items-center text-center">
                        <img src="https://placehold.co/100x100/B2EBF2/000000?text=Sensors" alt="Medical Electronics Icon" class="w-24 h-24 mb-6 rounded-full border-4 border-green-200 p-2">
                        <h3 class="text-2xl font-semibold text-green-800 mb-4">Medical Electronics & Physiological Instrumentation</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            Explore the foundational electronics and advanced sensors used in physiological monitoring. Key courses include:
                        </p>
                        <ul class="text-left text-gray-700 list-disc list-inside mb-6 space-y-2">
                            <li class="font-medium text-green-700">Biomedical Sensors: Design & Application</li>
                            <li class="font-medium text-green-700">Signal Acquisition & Processing for Bio-signals</li>
                            <li class="font-medium text-green-700">ECG, EMG, EEG Systems Analysis</li>
                            <li class="font-medium text-green-700">Medical Device Circuitry</li>
                        </ul>
                        <a href="#" class="text-green-600 hover:text-green-800 font-medium transition-colors duration-200">View Courses &rarr;</a>
                    </div>

                    <div class="bg-purple-50 p-8 rounded-xl shadow-lg card-hover-effect flex flex-col items-center text-center">
                        <img src="https://placehold.co/100x100/D1C4E9/000000?text=Prosthetics" alt="Biomechanics Icon" class="w-24 h-24 mb-6 rounded-full border-4 border-purple-200 p-2">
                        <h3 class="text-2xl font-semibold text-purple-800 mb-4">Biomechanics & Rehabilitation Engineering</h3>
                        <p class="text-gray-700 leading-relaxed mb-6">
                            Delve into the mechanics of human movement and the design of devices that restore function and improve quality of life. Courses cover prosthetics, orthotics, and advanced rehabilitation technologies.
                        </p>
                        <button id="exploreRehabCoursesBtn" class="bg-purple-600 text-white hover:bg-purple-700 px-6 py-3 rounded-full font-semibold shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300">
                            Explore Courses
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <section id="courseDetails" class="py-16 md:py-24 bg-gray-100 hidden">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-bold text-center text-gray-800 mb-12">Featured Course Modules</h2>
                <div id="courseList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    </div>
            </div>
        </section>
    </main>

    <footer id="contact" class="bg-gray-800 text-white py-10">
        <div class="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center text-center md:text-left">
            <div class="mb-6 md:mb-0">
                <h4 class="text-xl font-semibold mb-2">BioMed LMS</h4>
                <p class="text-gray-400">Empowering future biomedical innovators.</p>
            </div>
            
            <div class="mb-6 md:mb-0">
                <h4 class="text-xl font-semibold mb-2">Quick Links</h4>
                <ul class="space-y-1">
                    <li><a href="#home" class="text-gray-400 hover:text-white transition-colors duration-200">Home</a></li>
                    <li><a href="#divisions" class="text-gray-400 hover:text-white transition-colors duration-200">Courses</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">Privacy Policy</a></li>
                </ul>
            </div>

            <div>
                <h4 class="text-xl font-semibold mb-2">Connect With Us</h4>
                <div class="flex justify-center md:justify-start space-x-4 mb-4">
                    <a href="https://linkedin.com/biomedlms" target="_blank" class="social-icon text-gray-400 hover:text-blue-400">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2zM4 6a2 2 0 110-4 2 2 0 010 4z"></path>
                        </svg>
                    </a>
                    <a href="https://twitter.com/biomedlms" target="_blank" class="social-icon text-gray-400 hover:text-blue-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M8.29 20.251c1.803 0 3.533-.518 5.01-1.498 1.477-.98 2.684-2.35 3.59-3.997.906-1.647 1.359-3.484 1.359-5.41 0-1.926-.453-3.763-1.359-5.41-.906-1.647-2.113-3.017-3.59-3.997-1.477-.98-3.207-1.498-5.01-1.498-1.803 0-3.533.518-5.01 1.498-1.477.98-2.684 2.35-3.59 3.997-.906 1.647-1.359 3.484-1.359 5.41 0 1.926.453 3.763 1.359 5.41.906 1.647 2.113 3.017 3.59 3.997 1.477.98 3.207 1.498 5.01 1.498zM12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm3.707 7.293a1 1 0 00-1.414-1.414L12 9.586l-2.293-2.293a1 1 0 00-1.414 1.414L10.586 12l-2.293 2.293a1 1 0 001.414 1.414L12 14.414l2.293 2.293a1 1 0 001.414-1.414L13.414 12l2.293-2.293z"></path>
                        </svg>
                    </a>
                    <a href="mailto:<EMAIL>" class="social-icon text-gray-400 hover:text-red-400">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"></path>
                        </svg>
                    </a>
                </div>
                <p class="text-gray-400">Email: <EMAIL></p>
                <p class="text-gray-400">Phone: +249912867327, +966538076790</p>
            </div>
        </div>
        <div class="text-center text-gray-500 text-sm mt-8 border-t border-gray-700 pt-6">
            <p>Author: Dr. Mohammed Yagoub Esmail, SUST-BME 2025</p>
            <p>Copyright: <EMAIL></p>
            <p>© 2025 BioMed LMS. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Dynamic course list reveal
            const exploreCoursesBtn = document.getElementById('exploreCoursesBtn');
            const exploreRehabCoursesBtn = document.getElementById('exploreRehabCoursesBtn');
            const courseDetailsSection = document.getElementById('courseDetails');
            const courseListDiv = document.getElementById('courseList');

            const courses = [
                { id: 'img101', title: 'Principles of Medical Imaging', description: 'Covers fundamental principles of MRI, CT, PET, and ultrasound imaging modalities.', keyTopics: ['MRI Physics', 'CT Reconstruction', 'Ultrasound Signal Processing', 'Image Contrast Agents'] },
                { id: 'img102', title: 'Advanced Diagnostic Instrumentation', description: 'Focuses on the design and calibration of complex diagnostic equipment.', keyTopics: ['X-ray Systems', 'Nuclear Medicine', 'Optical Imaging', 'System Integration'] },
                { id: 'elec201', title: 'Biomedical Sensors & Signal Acquisition', description: 'Introduces sensors for physiological signal monitoring and methods of analog-to-digital conversion.', keyTopics: ['ECG Electrodes', 'Sensor Calibration', 'DAQ Systems', 'Signal Filtering'] },
                { id: 'elec202', title: 'Bio-signal Processing Techniques', description: 'Explores algorithms for noise reduction, feature extraction, and analysis of biological signals.', keyTopics: ['Filtering', 'Wavelet Analysis', 'Machine Learning for Bio-signals', 'Pattern Recognition'] },
                { id: 'reh301', title: 'Human Motion Analysis', description: 'Explores biomechanics of movement and technologies for capturing and analyzing motion.', keyTopics: ['Kinematic Modeling', 'Force Platforms', 'Gait Cycle Analysis'] },
                { id: 'reh302', title: 'Prosthetics & Orthotics Design', description: 'Covers the principles of designing and fitting prosthetic and orthotic devices.', keyTopics: ['Material Science', 'Biomaterials', 'CAD/CAM for Prosthetics', 'Patient Interface'] }
            ];

            const renderCourses = () => {
                courseListDiv.innerHTML = ''; // Clear previous content
                courses.forEach(course => {
                    const courseCard = document.createElement('div');
                    courseCard.className = 'bg-white p-6 rounded-xl shadow-md card-hover-effect';
                    courseCard.innerHTML = `
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">${course.title}</h3>
                        <p class="text-gray-700 text-sm mb-4">${course.description}</p>
                        <h4 class="text-md font-medium text-gray-800 mb-2">Key Topics:</h4>
                        <ul class="list-disc list-inside text-gray-600 text-sm space-y-1">
                            ${course.keyTopics.map(topic => `<li>${topic}</li>`).join('')}
                        </ul>
                    `;
                    courseListDiv.appendChild(courseCard);
                });
            };

            const toggleCourseDetails = () => {
                if (courseDetailsSection.classList.contains('hidden')) {
                    renderCourses();
                    courseDetailsSection.classList.remove('hidden');
                    // Scroll to the newly revealed section
                    courseDetailsSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                    courseDetailsSection.classList.add('hidden');
                }
            };

            exploreCoursesBtn.addEventListener('click', toggleCourseDetails);
            exploreRehabCoursesBtn.addEventListener('click', toggleCourseDetails);
        });
    </script>
</body>
</html>
