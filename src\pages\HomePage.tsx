import React from 'react';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  const features = [
    {
      title: 'Discover LMS Platforms',
      description: 'Explore and compare various biomedical learning management systems to find the perfect fit for your educational needs.',
      icon: '🔍',
      link: '/discover',
      color: 'bg-blue-500'
    },
    {
      title: 'Track Your Progress',
      description: 'Monitor your learning journey across multiple platforms and keep track of your biomedical education progress.',
      icon: '📊',
      link: '/tracker',
      color: 'bg-green-500'
    },
    {
      title: 'Subject Categories',
      description: 'Explore three core divisions: Biomedical Imaging, Electrophysiological Instrumentation, and Biomechanics & Rehabilitation.',
      icon: '📚',
      link: '/subject-categories',
      color: 'bg-purple-500'
    },
    {
      title: 'Core Subjects',
      description: 'Access comprehensive resources for essential biomedical subjects including anatomy, physiology, and more.',
      icon: '🔬',
      link: '/subjects',
      color: 'bg-indigo-500'
    },
    {
      title: 'Interactive Learning',
      description: 'Engage with interactive content, simulations, and virtual labs designed for biomedical education.',
      icon: '🧬',
      link: '/interactive',
      color: 'bg-red-500'
    }
  ];

  const stats = [
    { number: '50+', label: 'LMS Platforms', icon: '🏥' },
    { number: '1000+', label: 'Learning Resources', icon: '📖' },
    { number: '25+', label: 'Subject Areas', icon: '🔬' },
    { number: '10k+', label: 'Active Learners', icon: '👥' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Welcome to{' '}
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                BioMed LMS
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Your comprehensive platform for biomedical education. Discover, track, and excel in your medical learning journey.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link
              to="/discover"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Start Exploring
            </Link>
            <Link
              to="/subject-categories"
              className="bg-white hover:bg-gray-50 text-blue-600 border-2 border-blue-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Browse Subject Categories
            </Link>
          </div>

          {/* Hero Image/Illustration Placeholder */}
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl p-8 shadow-2xl">
              <div className="text-6xl mb-4">🏥</div>
              <p className="text-gray-600 text-lg">
                Advanced Learning Management System for Biomedical Education
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl mb-2">{stat.icon}</div>
                <div className="text-3xl font-bold text-gray-900 mb-1">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for Biomedical Learning
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive tools and resources designed specifically for biomedical education and professional development.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Link
                key={index}
                to={feature.link}
                className="group bg-white rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center text-white text-2xl mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
                <div className="mt-4 text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                  Learn more →
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-green-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Biomedical Education?
          </h2>
          <p className="text-xl text-blue-100 mb-8 leading-relaxed">
            Join thousands of medical students and professionals who are advancing their careers with our comprehensive learning platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/discover"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              Get Started Today
            </Link>
            <Link
              to="/tracker"
              className="bg-transparent text-white border-2 border-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-200 transform hover:scale-105"
            >
              Track Progress
            </Link>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">
            Need Help Getting Started?
          </h3>
          <p className="text-gray-600 mb-6">
            Our support team is here to help you make the most of your biomedical learning experience.
          </p>
          <button type="button" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            Contact Support
          </button>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
