/* BioMed LMS - Component Specific Styles */

/* Loading Spinner Component */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal Component */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card Components */
.lms-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
  cursor: pointer;
}

.lms-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-300);
}

.lms-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
}

.lms-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.lms-card-subtitle {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin: 0;
}

.lms-card-content {
  color: var(--gray-700);
  line-height: 1.6;
}

.lms-card-footer {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Vital Sign Card */
.vital-sign-card {
  background: linear-gradient(135deg, var(--primary-50) 0%, white 100%);
  border: 2px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all 0.3s ease;
}

.vital-sign-card:hover {
  border-color: var(--primary-400);
  box-shadow: var(--shadow-lg);
}

.vital-sign-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-700);
  margin: var(--spacing-sm) 0;
}

.vital-sign-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.vital-sign-unit {
  font-size: 1rem;
  color: var(--gray-500);
  margin-left: var(--spacing-xs);
}

/* ECG Display */
.ecg-display {
  background: #000;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.ecg-grid {
  background-image: 
    linear-gradient(rgba(0, 255, 0, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 0, 0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  width: 100%;
  height: 200px;
}

.ecg-line {
  stroke: #00ff00;
  stroke-width: 2;
  fill: none;
  filter: drop-shadow(0 0 3px #00ff00);
}

/* Patient Monitor */
.patient-monitor {
  background: #1a1a1a;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  color: white;
  font-family: 'Courier New', monospace;
}

.monitor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid #333;
}

.monitor-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background-color: #00ff00;
}

.status-indicator.warning {
  background-color: #ffaa00;
}

.status-indicator.critical {
  background-color: #ff0000;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Scenario Selector */
.scenario-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.scenario-option {
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.scenario-option:hover {
  border-color: var(--primary-400);
  box-shadow: var(--shadow-md);
}

.scenario-option.selected {
  border-color: var(--primary-600);
  background: var(--primary-50);
}

.scenario-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.scenario-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.scenario-description {
  color: var(--gray-600);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Error Display */
.error-display {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.error-icon {
  color: #dc2626;
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.error-title {
  color: #991b1b;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.error-message {
  color: #7f1d1d;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    margin: var(--spacing-md);
    padding: var(--spacing-lg);
  }
  
  .scenario-selector {
    grid-template-columns: 1fr;
  }
  
  .lms-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .lms-card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .vital-sign-value {
    font-size: 1.5rem;
  }
  
  .patient-monitor {
    padding: var(--spacing-lg);
  }
  
  .monitor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
