
import React from 'react';
import { LMSInfo } from '../types';

interface LMSCardProps {
  lms: LMSInfo;
}

const LMSCard: React.FC<LMSCardProps> = ({ lms }) => {
  return (
    <div className="bg-white shadow-lg rounded-xl overflow-hidden transform hover:scale-105 transition-transform duration-300 ease-in-out">
      <div className="p-6">
        <h3 className="text-xl font-semibold text-primary-700 mb-2">{lms.name}</h3>
        <p className="text-gray-600 text-sm mb-4">{lms.description}</p>
        
        <div className="mb-4">
          <h4 className="text-md font-semibold text-gray-700 mb-1">Key Features:</h4>
          <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
            {lms.keyFeatures.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h4 className="text-md font-semibold text-gray-700 mb-1">Target Audience:</h4>
          <p className="text-sm text-gray-600">{lms.targetAudience}</p>
        </div>
      </div>
    </div>
  );
};

export default LMSCard;
