import React, { useState } from 'react';
import { Link } from 'react-router-dom';

interface Subject {
  id: string;
  name: string;
  description: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  icon: string;
  modules: string[];
  prerequisites?: string[];
  learningOutcomes: string[];
  credits: number;
}

interface Division {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  subjects: Subject[];
}

const SubjectCategoriesPage: React.FC = () => {
  const [selectedDivision, setSelectedDivision] = useState<string | null>(null);

  const divisions: Division[] = [
    {
      id: 'biomedical-imaging',
      title: 'Biomedical Imaging Instrumentation Technology',
      description: 'Advanced imaging technologies and instrumentation used in medical diagnosis and research.',
      color: 'from-blue-500 to-blue-700',
      icon: '🔬',
      subjects: [
        {
          id: 'mri-fundamentals',
          name: 'MRI Fundamentals',
          description: 'Magnetic Resonance Imaging principles, physics, and clinical applications.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧲',
          credits: 3,
          modules: [
            'MRI Physics and Magnetic Field Theory',
            'Pulse Sequences and Signal Generation',
            'Image Reconstruction Algorithms',
            'Contrast Mechanisms and Tissue Properties',
            'MRI Safety and Quality Control',
            'Clinical Applications and Case Studies'
          ],
          prerequisites: ['Basic Physics', 'Mathematics'],
          learningOutcomes: [
            'Understand fundamental MRI physics principles',
            'Analyze different pulse sequences and their applications',
            'Evaluate image quality and artifacts',
            'Apply safety protocols in MRI environments'
          ]
        },
        {
          id: 'ct-imaging',
          name: 'CT Imaging Technology',
          description: 'Computed Tomography systems, image reconstruction, and quality control.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '💻',
          credits: 3,
          modules: [
            'X-ray Physics and Attenuation',
            'CT Scanner Components and Design',
            'Image Reconstruction Techniques',
            'Contrast Agents and Protocols',
            'Radiation Dose and Safety',
            'Quality Assurance and Maintenance'
          ],
          prerequisites: ['Radiographic Physics', 'Basic Anatomy'],
          learningOutcomes: [
            'Explain CT imaging principles and technology',
            'Optimize scanning parameters for different examinations',
            'Implement radiation safety measures',
            'Perform quality control procedures'
          ]
        },
        {
          id: 'ultrasound-physics',
          name: 'Ultrasound Physics & Instrumentation',
          description: 'Ultrasound wave physics, transducers, and imaging system components.',
          duration: '7 weeks',
          level: 'Beginner',
          icon: '🌊',
          credits: 3,
          modules: [
            'Acoustic Wave Properties and Propagation',
            'Transducer Design and Beam Characteristics',
            'Doppler Effect and Flow Measurement',
            'Image Formation and Display',
            'Artifacts and Image Optimization',
            'Safety Considerations and Bioeffects'
          ],
          learningOutcomes: [
            'Understand ultrasound wave physics',
            'Operate ultrasound equipment effectively',
            'Recognize and minimize imaging artifacts',
            'Apply safety guidelines for ultrasound use'
          ]
        },
        {
          id: 'nuclear-medicine',
          name: 'Nuclear Medicine Imaging',
          description: 'Radiopharmaceuticals, gamma cameras, and SPECT/PET imaging systems.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '☢️',
          credits: 4,
          modules: [
            'Radioactive Decay and Nuclear Physics',
            'Radiopharmaceuticals and Tracers',
            'Gamma Camera and Detector Systems',
            'SPECT Imaging Principles and Reconstruction',
            'PET Imaging Technology and Applications',
            'Radiation Safety and Regulatory Compliance',
            'Clinical Nuclear Medicine Procedures',
            'Quality Control and Performance Testing'
          ],
          prerequisites: ['Nuclear Physics', 'Radiation Safety', 'Medical Imaging Fundamentals'],
          learningOutcomes: [
            'Understand nuclear decay processes and radiation interactions',
            'Analyze radiopharmaceutical properties and biodistribution',
            'Operate nuclear medicine imaging equipment',
            'Implement comprehensive radiation safety programs',
            'Interpret nuclear medicine studies'
          ]
        },
        {
          id: 'digital-radiography',
          name: 'Digital Radiography',
          description: 'Digital X-ray systems, image processing, and PACS integration.',
          duration: '5 weeks',
          level: 'Beginner',
          icon: '📷',
          credits: 2,
          modules: [
            'Digital Detector Technologies',
            'Image Acquisition and Processing',
            'DICOM Standards and Protocols',
            'PACS and RIS Integration',
            'Image Quality and Optimization',
            'Workflow and System Management'
          ],
          learningOutcomes: [
            'Compare different digital detector technologies',
            'Optimize image acquisition parameters',
            'Understand DICOM and healthcare informatics',
            'Manage digital imaging workflows'
          ]
        },
        {
          id: 'image-processing',
          name: 'Medical Image Processing',
          description: 'Image enhancement, segmentation, and analysis algorithms.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🖼️',
          credits: 4,
          modules: [
            'Digital Image Fundamentals',
            'Image Enhancement Techniques',
            'Filtering and Noise Reduction',
            'Image Segmentation Methods',
            'Feature Extraction and Analysis',
            'Machine Learning in Medical Imaging',
            'Image Registration and Fusion',
            'Quantitative Image Analysis'
          ],
          prerequisites: ['Programming', 'Mathematics', 'Medical Imaging'],
          learningOutcomes: [
            'Apply image processing algorithms to medical images',
            'Develop automated analysis tools',
            'Implement machine learning techniques',
            'Perform quantitative image analysis'
          ]
        }
      ]
    },
    {
      id: 'electrophysiological',
      title: 'Electrophysiological Instrumentation and Measurements',
      description: 'Electrical activity measurement and analysis in biological systems.',
      color: 'from-green-500 to-green-700',
      icon: '⚡',
      subjects: [
        {
          id: 'ecg-fundamentals',
          name: 'ECG Fundamentals',
          description: 'Electrocardiography principles, lead systems, and signal acquisition.',
          duration: '6 weeks',
          level: 'Beginner',
          icon: '💓',
          credits: 3,
          modules: [
            'Cardiac Anatomy and Electrophysiology',
            'ECG Lead Systems and Electrode Placement',
            'Signal Acquisition and Amplification',
            'Normal ECG Waveforms and Intervals',
            'Arrhythmia Recognition and Analysis',
            'ECG Equipment and Quality Control'
          ],
          learningOutcomes: [
            'Understand cardiac electrical conduction system',
            'Perform proper ECG electrode placement',
            'Recognize normal and abnormal ECG patterns',
            'Operate ECG equipment safely and effectively'
          ]
        },
        {
          id: 'eeg-analysis',
          name: 'EEG Signal Analysis',
          description: 'Electroencephalography recording, artifact removal, and interpretation.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧠',
          credits: 4,
          modules: [
            'Neuroanatomy and Brain Electrical Activity',
            'EEG Electrode Systems and Montages',
            'Signal Processing and Filtering',
            'Artifact Recognition and Removal',
            'Frequency Domain Analysis',
            'Event-Related Potentials',
            'Sleep Studies and Polysomnography',
            'Clinical EEG Interpretation'
          ],
          prerequisites: ['Neuroanatomy', 'Signal Processing Basics'],
          learningOutcomes: [
            'Understand brain electrical activity patterns',
            'Apply advanced signal processing techniques',
            'Identify and remove various artifacts',
            'Interpret clinical EEG recordings'
          ]
        },
        {
          id: 'emg-systems',
          name: 'EMG Measurement Systems',
          description: 'Electromyography instrumentation and muscle activity analysis.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '💪',
          credits: 3,
          modules: [
            'Muscle Physiology and Motor Units',
            'Surface and Intramuscular EMG',
            'EMG Signal Characteristics',
            'Amplification and Filtering Systems',
            'Motion Artifact and Noise Reduction',
            'EMG Analysis Techniques',
            'Clinical and Research Applications'
          ],
          prerequisites: ['Muscle Physiology', 'Basic Electronics'],
          learningOutcomes: [
            'Understand muscle electrical activity',
            'Design EMG measurement systems',
            'Analyze EMG signals quantitatively',
            'Apply EMG in clinical and research settings'
          ]
        },
        {
          id: 'bioamplifiers',
          name: 'Biomedical Amplifiers',
          description: 'Design and characteristics of amplifiers for biological signals.',
          duration: '5 weeks',
          level: 'Advanced',
          icon: '📡',
          credits: 3,
          modules: [
            'Operational Amplifier Fundamentals',
            'Differential Amplifier Design',
            'Instrumentation Amplifiers',
            'Isolation Amplifiers and Safety',
            'Noise Analysis and Reduction',
            'Frequency Response and Bandwidth'
          ],
          prerequisites: ['Electronics', 'Circuit Analysis', 'Signal Processing'],
          learningOutcomes: [
            'Design biomedical amplifier circuits',
            'Analyze noise sources and mitigation strategies',
            'Implement electrical safety measures',
            'Optimize amplifier performance for specific applications'
          ]
        },
        {
          id: 'signal-conditioning',
          name: 'Signal Conditioning',
          description: 'Filtering, amplification, and noise reduction in biomedical signals.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '🔧',
          credits: 3,
          modules: [
            'Analog Filter Design',
            'Digital Signal Processing',
            'Sampling and Quantization',
            'Noise Sources and Characteristics',
            'Adaptive Filtering Techniques',
            'Real-time Signal Processing'
          ],
          prerequisites: ['Signal Processing', 'Mathematics'],
          learningOutcomes: [
            'Design appropriate filtering systems',
            'Implement digital signal processing algorithms',
            'Optimize signal-to-noise ratio',
            'Develop real-time processing solutions'
          ]
        },
        {
          id: 'neural-interfaces',
          name: 'Neural Interface Technology',
          description: 'Brain-computer interfaces and neural signal processing.',
          duration: '12 weeks',
          level: 'Advanced',
          icon: '🔌',
          credits: 5,
          modules: [
            'Neuroscience Fundamentals',
            'Neural Signal Acquisition',
            'Microelectrode Arrays and Implants',
            'Signal Processing for Neural Data',
            'Machine Learning for BCI',
            'Closed-loop Control Systems',
            'Ethical and Safety Considerations',
            'Clinical Applications and Case Studies'
          ],
          prerequisites: ['Neuroscience', 'Signal Processing', 'Programming', 'Machine Learning'],
          learningOutcomes: [
            'Understand neural signal characteristics',
            'Design brain-computer interface systems',
            'Implement machine learning algorithms for neural decoding',
            'Address ethical and safety considerations in neural interfaces'
          ]
        }
      ]
    },
    {
      id: 'biomechanics-rehab',
      title: 'Biomechanics and Rehabilitation Engineering',
      description: 'Mechanical principles applied to biological systems and rehabilitation technologies.',
      color: 'from-purple-500 to-purple-700',
      icon: '🦴',
      subjects: [
        {
          id: 'gait-analysis',
          name: 'Gait Analysis',
          description: 'Motion capture systems and biomechanical analysis of human walking.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🚶',
          credits: 4,
          modules: [
            'Human Locomotion Fundamentals',
            'Motion Capture Technologies',
            'Force Plate and Pressure Systems',
            'Kinematic and Kinetic Analysis',
            'Gait Cycle and Parameters',
            'Pathological Gait Patterns',
            'Data Processing and Interpretation',
            'Clinical Applications'
          ],
          prerequisites: ['Biomechanics', 'Anatomy', 'Physics'],
          learningOutcomes: [
            'Understand normal and pathological gait patterns',
            'Operate motion capture and force measurement systems',
            'Analyze kinematic and kinetic data',
            'Apply gait analysis in clinical settings'
          ]
        },
        {
          id: 'prosthetics-design',
          name: 'Prosthetics Design',
          description: 'Design principles and materials for artificial limbs and devices.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '🦿',
          credits: 4,
          modules: [
            'Amputation Levels and Residual Limb Anatomy',
            'Biomechanical Principles of Prosthetics',
            'Materials Science for Prosthetics',
            'Socket Design and Fitting',
            'Mechanical and Microprocessor Components',
            'Control Systems and User Interfaces',
            'Manufacturing and Fabrication',
            'Clinical Evaluation and Outcomes'
          ],
          prerequisites: ['Biomechanics', 'Materials Science', 'Anatomy'],
          learningOutcomes: [
            'Design prosthetic devices for different amputation levels',
            'Select appropriate materials and components',
            'Understand user needs and functional requirements',
            'Evaluate prosthetic performance and outcomes'
          ]
        },
        {
          id: 'orthotics-systems',
          name: 'Orthotic Systems',
          description: 'Supportive devices for musculoskeletal disorders and rehabilitation.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '🦵',
          credits: 3,
          modules: [
            'Musculoskeletal Anatomy and Pathology',
            'Biomechanical Principles of Orthotics',
            'Materials and Manufacturing Processes',
            'Spinal Orthoses Design',
            'Lower Limb Orthoses',
            'Upper Limb Orthoses',
            'Pediatric Considerations'
          ],
          prerequisites: ['Anatomy', 'Biomechanics'],
          learningOutcomes: [
            'Understand orthotic principles and applications',
            'Design orthotic devices for various conditions',
            'Select appropriate materials and fabrication methods',
            'Consider patient-specific factors in orthotic design'
          ]
        },
        {
          id: 'biomaterials',
          name: 'Biomaterials Engineering',
          description: 'Materials science applied to medical devices and implants.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🧪',
          credits: 4,
          modules: [
            'Materials Science Fundamentals',
            'Biocompatibility and Bioactivity',
            'Metallic Biomaterials',
            'Ceramic and Composite Materials',
            'Polymeric Biomaterials',
            'Surface Modification Techniques',
            'Degradation and Corrosion',
            'Regulatory and Testing Standards'
          ],
          prerequisites: ['Materials Science', 'Chemistry', 'Biology'],
          learningOutcomes: [
            'Understand material-tissue interactions',
            'Select appropriate materials for medical applications',
            'Design biocompatible material systems',
            'Evaluate material performance and safety'
          ]
        },
        {
          id: 'rehabilitation-robotics',
          name: 'Rehabilitation Robotics',
          description: 'Robotic systems for physical therapy and motor recovery.',
          duration: '11 weeks',
          level: 'Advanced',
          icon: '🤖',
          credits: 5,
          modules: [
            'Robotics Fundamentals',
            'Human Motor Control and Learning',
            'Rehabilitation Principles',
            'Exoskeleton Design and Control',
            'End-effector Based Robots',
            'Virtual Reality Integration',
            'Adaptive Control Systems',
            'Clinical Validation and Outcomes',
            'Emerging Technologies'
          ],
          prerequisites: ['Robotics', 'Control Systems', 'Biomechanics', 'Programming'],
          learningOutcomes: [
            'Design rehabilitation robotic systems',
            'Implement adaptive control algorithms',
            'Integrate virtual reality with robotic therapy',
            'Evaluate clinical effectiveness of robotic interventions'
          ]
        },
        {
          id: 'biomechanical-modeling',
          name: 'Biomechanical Modeling',
          description: 'Mathematical models of human movement and tissue mechanics.',
          duration: '8 weeks',
          level: 'Advanced',
          icon: '📊',
          credits: 4,
          modules: [
            'Mathematical Modeling Fundamentals',
            'Musculoskeletal System Modeling',
            'Finite Element Analysis',
            'Multibody Dynamics',
            'Tissue Mechanics and Constitutive Models',
            'Model Validation and Verification',
            'Computational Tools and Software',
            'Clinical Applications'
          ],
          prerequisites: ['Mathematics', 'Biomechanics', 'Programming', 'Physics'],
          learningOutcomes: [
            'Develop mathematical models of biological systems',
            'Apply finite element analysis to biomechanical problems',
            'Validate computational models with experimental data',
            'Use modeling for clinical decision support'
          ]
        }
      ]
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Biomedical Engineering Subject Categories
          </h1>
          <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Explore comprehensive courses across three core divisions of biomedical engineering
          </p>
          <div className="mt-8">
            <Link
              to="/"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </section>

      {/* Division Overview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Specialization
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select a division to explore detailed courses and learning paths
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {divisions.map((division) => (
              <div
                key={division.id}
                className={`relative overflow-hidden rounded-2xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-105 ${
                  selectedDivision === division.id ? 'ring-4 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedDivision(selectedDivision === division.id ? null : division.id)}
              >
                <div className={`bg-gradient-to-br ${division.color} p-8 text-white`}>
                  <div className="text-4xl mb-4">{division.icon}</div>
                  <h3 className="text-xl font-bold mb-3">{division.title}</h3>
                  <p className="text-blue-100 leading-relaxed">{division.description}</p>
                  <div className="mt-6">
                    <span className="inline-flex items-center text-sm font-medium">
                      {division.subjects.length} Courses Available
                      <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Selected Division Details */}
          {selectedDivision && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              {divisions
                .filter(division => division.id === selectedDivision)
                .map(division => (
                  <div key={division.id}>
                    <div className="flex items-center mb-8">
                      <div className="text-3xl mr-4">{division.icon}</div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{division.title}</h3>
                        <p className="text-gray-600">{division.description}</p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {division.subjects.map((subject) => (
                        <div
                          key={subject.id}
                          className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-blue-300"
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="text-2xl">{subject.icon}</div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(subject.level)}`}>
                              {subject.level}
                            </span>
                          </div>

                          <h4 className="text-lg font-semibold text-gray-900 mb-2">
                            {subject.name}
                          </h4>

                          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                            {subject.description}
                          </p>

                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-500">
                                📅 {subject.duration}
                              </span>
                              <span className="text-sm font-medium text-blue-600">
                                {subject.credits} Credits
                              </span>
                            </div>

                            {subject.prerequisites && (
                              <div className="mb-3">
                                <p className="text-xs font-medium text-gray-700 mb-1">Prerequisites:</p>
                                <div className="flex flex-wrap gap-1">
                                  {subject.prerequisites.map((prereq, index) => (
                                    <span key={index} className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                                      {prereq}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}

                            <div className="mb-3">
                              <p className="text-xs font-medium text-gray-700 mb-1">Course Modules ({subject.modules.length}):</p>
                              <div className="max-h-20 overflow-y-auto">
                                <ul className="text-xs text-gray-600 space-y-1">
                                  {subject.modules.slice(0, 3).map((module, index) => (
                                    <li key={index} className="flex items-start">
                                      <span className="text-blue-500 mr-1">•</span>
                                      {module}
                                    </li>
                                  ))}
                                  {subject.modules.length > 3 && (
                                    <li className="text-gray-500 italic">
                                      +{subject.modules.length - 3} more modules...
                                    </li>
                                  )}
                                </ul>
                              </div>
                            </div>

                            <button
                              type="button"
                              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
                            >
                              View Full Course Details
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gray-900 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Start Your Learning Journey?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of biomedical engineering students advancing their careers
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/tracker"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Track Your Progress
            </Link>
            <Link
              to="/discover"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Discover More LMS
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SubjectCategoriesPage;
