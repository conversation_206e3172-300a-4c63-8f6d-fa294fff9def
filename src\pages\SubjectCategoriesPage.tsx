import React, { useState } from 'react';
import { Link } from 'react-router-dom';

interface Subject {
  id: string;
  name: string;
  description: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  icon: string;
}

interface Division {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  subjects: Subject[];
}

const SubjectCategoriesPage: React.FC = () => {
  const [selectedDivision, setSelectedDivision] = useState<string | null>(null);

  const divisions: Division[] = [
    {
      id: 'biomedical-imaging',
      title: 'Biomedical Imaging Instrumentation Technology',
      description: 'Advanced imaging technologies and instrumentation used in medical diagnosis and research.',
      color: 'from-blue-500 to-blue-700',
      icon: '🔬',
      subjects: [
        {
          id: 'mri-fundamentals',
          name: 'MRI Fundamentals',
          description: 'Magnetic Resonance Imaging principles, physics, and clinical applications.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧲'
        },
        {
          id: 'ct-imaging',
          name: 'CT Imaging Technology',
          description: 'Computed Tomography systems, image reconstruction, and quality control.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '💻'
        },
        {
          id: 'ultrasound-physics',
          name: 'Ultrasound Physics & Instrumentation',
          description: 'Ultrasound wave physics, transducers, and imaging system components.',
          duration: '7 weeks',
          level: 'Beginner',
          icon: '🌊'
        },
        {
          id: 'nuclear-medicine',
          name: 'Nuclear Medicine Imaging',
          description: 'Radiopharmaceuticals, gamma cameras, and SPECT/PET imaging systems.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '☢️'
        },
        {
          id: 'digital-radiography',
          name: 'Digital Radiography',
          description: 'Digital X-ray systems, image processing, and PACS integration.',
          duration: '5 weeks',
          level: 'Beginner',
          icon: '📷'
        },
        {
          id: 'image-processing',
          name: 'Medical Image Processing',
          description: 'Image enhancement, segmentation, and analysis algorithms.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🖼️'
        }
      ]
    },
    {
      id: 'electrophysiological',
      title: 'Electrophysiological Instrumentation and Measurements',
      description: 'Electrical activity measurement and analysis in biological systems.',
      color: 'from-green-500 to-green-700',
      icon: '⚡',
      subjects: [
        {
          id: 'ecg-fundamentals',
          name: 'ECG Fundamentals',
          description: 'Electrocardiography principles, lead systems, and signal acquisition.',
          duration: '6 weeks',
          level: 'Beginner',
          icon: '💓'
        },
        {
          id: 'eeg-analysis',
          name: 'EEG Signal Analysis',
          description: 'Electroencephalography recording, artifact removal, and interpretation.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧠'
        },
        {
          id: 'emg-systems',
          name: 'EMG Measurement Systems',
          description: 'Electromyography instrumentation and muscle activity analysis.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '💪'
        },
        {
          id: 'bioamplifiers',
          name: 'Biomedical Amplifiers',
          description: 'Design and characteristics of amplifiers for biological signals.',
          duration: '5 weeks',
          level: 'Advanced',
          icon: '📡'
        },
        {
          id: 'signal-conditioning',
          name: 'Signal Conditioning',
          description: 'Filtering, amplification, and noise reduction in biomedical signals.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '🔧'
        },
        {
          id: 'neural-interfaces',
          name: 'Neural Interface Technology',
          description: 'Brain-computer interfaces and neural signal processing.',
          duration: '12 weeks',
          level: 'Advanced',
          icon: '🔌'
        }
      ]
    },
    {
      id: 'biomechanics-rehab',
      title: 'Biomechanics and Rehabilitation Engineering',
      description: 'Mechanical principles applied to biological systems and rehabilitation technologies.',
      color: 'from-purple-500 to-purple-700',
      icon: '🦴',
      subjects: [
        {
          id: 'gait-analysis',
          name: 'Gait Analysis',
          description: 'Motion capture systems and biomechanical analysis of human walking.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🚶'
        },
        {
          id: 'prosthetics-design',
          name: 'Prosthetics Design',
          description: 'Design principles and materials for artificial limbs and devices.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '🦿'
        },
        {
          id: 'orthotics-systems',
          name: 'Orthotic Systems',
          description: 'Supportive devices for musculoskeletal disorders and rehabilitation.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '🦵'
        },
        {
          id: 'biomaterials',
          name: 'Biomaterials Engineering',
          description: 'Materials science applied to medical devices and implants.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🧪'
        },
        {
          id: 'rehabilitation-robotics',
          name: 'Rehabilitation Robotics',
          description: 'Robotic systems for physical therapy and motor recovery.',
          duration: '11 weeks',
          level: 'Advanced',
          icon: '🤖'
        },
        {
          id: 'biomechanical-modeling',
          name: 'Biomechanical Modeling',
          description: 'Mathematical models of human movement and tissue mechanics.',
          duration: '8 weeks',
          level: 'Advanced',
          icon: '📊'
        }
      ]
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Biomedical Engineering Subject Categories
          </h1>
          <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Explore comprehensive courses across three core divisions of biomedical engineering
          </p>
          <div className="mt-8">
            <Link
              to="/"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </section>

      {/* Division Overview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Specialization
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select a division to explore detailed courses and learning paths
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {divisions.map((division) => (
              <div
                key={division.id}
                className={`relative overflow-hidden rounded-2xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-105 ${
                  selectedDivision === division.id ? 'ring-4 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedDivision(selectedDivision === division.id ? null : division.id)}
              >
                <div className={`bg-gradient-to-br ${division.color} p-8 text-white`}>
                  <div className="text-4xl mb-4">{division.icon}</div>
                  <h3 className="text-xl font-bold mb-3">{division.title}</h3>
                  <p className="text-blue-100 leading-relaxed">{division.description}</p>
                  <div className="mt-6">
                    <span className="inline-flex items-center text-sm font-medium">
                      {division.subjects.length} Courses Available
                      <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Selected Division Details */}
          {selectedDivision && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              {divisions
                .filter(division => division.id === selectedDivision)
                .map(division => (
                  <div key={division.id}>
                    <div className="flex items-center mb-8">
                      <div className="text-3xl mr-4">{division.icon}</div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{division.title}</h3>
                        <p className="text-gray-600">{division.description}</p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {division.subjects.map((subject) => (
                        <div
                          key={subject.id}
                          className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-blue-300"
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="text-2xl">{subject.icon}</div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(subject.level)}`}>
                              {subject.level}
                            </span>
                          </div>
                          
                          <h4 className="text-lg font-semibold text-gray-900 mb-2">
                            {subject.name}
                          </h4>
                          
                          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                            {subject.description}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-500">
                              📅 {subject.duration}
                            </span>
                            <button className="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                              Learn More →
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gray-900 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Start Your Learning Journey?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of biomedical engineering students advancing their careers
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/tracker"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Track Your Progress
            </Link>
            <Link
              to="/discover"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Discover More LMS
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SubjectCategoriesPage;
