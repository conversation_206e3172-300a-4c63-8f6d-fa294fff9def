
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import type { ECGPoint } from '../types';

interface ECGDisplayProps {
  waveform: ECGPoint[];
  description: string;
}

export const ECGDisplay: React.FC<ECGDisplayProps> = ({ waveform, description }) => {
  // Ensure waveform data is valid for Recharts
  const validWaveform = Array.isArray(waveform) && waveform.length > 0 
    ? waveform 
    : [{ time: 0, value: 0 }]; // Fallback to a single point if data is invalid/empty

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-lg col-span-1 md:col-span-2 h-[300px] md:h-[400px] flex flex-col">
      <h3 className="text-lg font-semibold text-blue-700 mb-1">ECG Waveform</h3>
      <p className="text-xs text-slate-500 mb-3">{description || "Electrocardiogram Reading"}</p>
      <div className="flex-grow">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={validWaveform} margin={{ top: 5, right: 20, left: -30, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis 
              dataKey="time" 
              tickLine={false} 
              axisLine={false} 
              tick={{ fontSize: 10, fill: '#718096' }} 
              label={{ value: "Time (ms)", position: "insideBottomRight", dy:10, fontSize: 10, fill: '#718096' }}
            />
            <YAxis 
              domain={[-1.5, 2.5]} 
              tickLine={false} 
              axisLine={false} 
              tick={{ fontSize: 10, fill: '#718096' }}
              allowDataOverflow={true}
            />
            <Tooltip 
              contentStyle={{ backgroundColor: 'rgba(255,255,255,0.9)', borderRadius: '0.5rem', borderColor: '#cbd5e0' }}
              itemStyle={{ color: '#1e40af' }}
              labelStyle={{ color: '#475569', fontWeight: 'bold' }}
            />
            <Legend verticalAlign="top" height={36} wrapperStyle={{fontSize: "12px"}}/>
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke="#ef4444" // red-500
              strokeWidth={2} 
              dot={false} 
              isAnimationActive={true}
              animationDuration={500}
              name="ECG (mV)"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
