
import React from 'react';
import { Routes, Route, NavLink } from 'react-router-dom';
import HomePage from './pages/HomePage';
import DiscoverLMSPage from './pages/DiscoverLMSPage';
import LMSTrackerPage from './pages/LMSTrackerPage';
import BiomedicalSubjectsPage from './pages/BiomedicalSubjectsPage';
import SubjectCategoriesPage from './pages/SubjectCategoriesPage';
import { GEMINI_API_KEY } from './constants';

const App: React.FC = () => {
  if (!GEMINI_API_KEY) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-100">
        <div className="p-8 bg-white shadow-lg rounded-lg text-center">
          <h1 className="text-2xl font-bold text-red-700 mb-4">API Key Missing</h1>
          <p className="text-gray-700">
            The Gemini API key is not configured. Please set the <code>API_KEY</code> environment variable.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 text-gray-800">
      <header className="bg-primary-700 text-white shadow-md">
        <nav className="container mx-auto px-6 py-4 flex justify-between items-center">
          <NavLink to="/" className="text-2xl font-bold hover:text-primary-200 transition-colors">
            BioMed LMS
          </NavLink>
          <div className="space-x-4">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive ? 'bg-primary-900 text-white' : 'hover:bg-primary-600 hover:text-white'
                }`
              }
            >
              Home
            </NavLink>
            <NavLink
              to="/discover"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive ? 'bg-primary-900 text-white' : 'hover:bg-primary-600 hover:text-white'
                }`
              }
            >
              Discover LMS
            </NavLink>
            <NavLink
              to="/tracker"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive ? 'bg-primary-900 text-white' : 'hover:bg-primary-600 hover:text-white'
                }`
              }
            >
              My LMS Tracker
            </NavLink>
            <NavLink
              to="/subject-categories"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive ? 'bg-primary-900 text-white' : 'hover:bg-primary-600 hover:text-white'
                }`
              }
            >
              Subject Categories
            </NavLink>
            <NavLink
              to="/subjects"
              className={({ isActive }) =>
                `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive ? 'bg-primary-900 text-white' : 'hover:bg-primary-600 hover:text-white'
                }`
              }
            >
              Core Subjects
            </NavLink>
          </div>
        </nav>
      </header>

      <main className="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/discover" element={<DiscoverLMSPage />} />
          <Route path="/tracker" element={<LMSTrackerPage />} />
          <Route path="/subject-categories" element={<SubjectCategoriesPage />} />
          <Route path="/subjects" element={<BiomedicalSubjectsPage />} />
        </Routes>
      </main>

      <footer className="bg-gray-800 text-white py-6 text-center">
        <p>&copy; {new Date().getFullYear()} BioMed LMS Explorer. All rights reserved.</p>
        <p className="text-sm text-gray-400">Powered by AI</p>
      </footer>
    </div>
  );
};

export default App;
