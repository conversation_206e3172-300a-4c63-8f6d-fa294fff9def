/**
 * BioMed LMS - Utility Functions
 * Common JavaScript utilities for the BioMed LMS application
 */

// DOM Utilities
export const DOM = {
  /**
   * Get element by ID
   * @param {string} id - Element ID
   * @returns {HTMLElement|null}
   */
  getElementById: (id) => document.getElementById(id),

  /**
   * Get elements by class name
   * @param {string} className - Class name
   * @returns {HTMLCollection}
   */
  getElementsByClassName: (className) => document.getElementsByClassName(className),

  /**
   * Query selector
   * @param {string} selector - CSS selector
   * @returns {HTMLElement|null}
   */
  querySelector: (selector) => document.querySelector(selector),

  /**
   * Query selector all
   * @param {string} selector - CSS selector
   * @returns {NodeList}
   */
  querySelectorAll: (selector) => document.querySelectorAll(selector),

  /**
   * Add event listener to element
   * @param {HTMLElement} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   */
  addEventListener: (element, event, handler) => {
    if (element && typeof handler === 'function') {
      element.addEventListener(event, handler);
    }
  },

  /**
   * Remove event listener from element
   * @param {HTMLElement} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   */
  removeEventListener: (element, event, handler) => {
    if (element && typeof handler === 'function') {
      element.removeEventListener(event, handler);
    }
  }
};

// Animation Utilities
export const Animation = {
  /**
   * Fade in element
   * @param {HTMLElement} element - Target element
   * @param {number} duration - Animation duration in ms
   */
  fadeIn: (element, duration = 300) => {
    if (!element) return;
    
    element.style.opacity = '0';
    element.style.display = 'block';
    
    const start = performance.now();
    
    const animate = (currentTime) => {
      const elapsed = currentTime - start;
      const progress = Math.min(elapsed / duration, 1);
      
      element.style.opacity = progress;
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  },

  /**
   * Fade out element
   * @param {HTMLElement} element - Target element
   * @param {number} duration - Animation duration in ms
   */
  fadeOut: (element, duration = 300) => {
    if (!element) return;
    
    const start = performance.now();
    const initialOpacity = parseFloat(getComputedStyle(element).opacity);
    
    const animate = (currentTime) => {
      const elapsed = currentTime - start;
      const progress = Math.min(elapsed / duration, 1);
      
      element.style.opacity = initialOpacity * (1 - progress);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        element.style.display = 'none';
      }
    };
    
    requestAnimationFrame(animate);
  },

  /**
   * Slide down element
   * @param {HTMLElement} element - Target element
   * @param {number} duration - Animation duration in ms
   */
  slideDown: (element, duration = 300) => {
    if (!element) return;
    
    element.style.height = '0';
    element.style.overflow = 'hidden';
    element.style.display = 'block';
    
    const targetHeight = element.scrollHeight;
    const start = performance.now();
    
    const animate = (currentTime) => {
      const elapsed = currentTime - start;
      const progress = Math.min(elapsed / duration, 1);
      
      element.style.height = `${targetHeight * progress}px`;
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        element.style.height = '';
        element.style.overflow = '';
      }
    };
    
    requestAnimationFrame(animate);
  }
};

// Form Utilities
export const Form = {
  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean}
   */
  validateEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate required fields
   * @param {HTMLFormElement} form - Form element
   * @returns {boolean}
   */
  validateRequired: (form) => {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.classList.add('error');
        isValid = false;
      } else {
        field.classList.remove('error');
      }
    });
    
    return isValid;
  },

  /**
   * Get form data as object
   * @param {HTMLFormElement} form - Form element
   * @returns {Object}
   */
  getFormData: (form) => {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    return data;
  }
};

// Storage Utilities
export const Storage = {
  /**
   * Set item in localStorage
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   */
  setItem: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  },

  /**
   * Get item from localStorage
   * @param {string} key - Storage key
   * @returns {any}
   */
  getItem: (key) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return null;
    }
  },

  /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   */
  removeItem: (key) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  },

  /**
   * Clear all localStorage
   */
  clear: () => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
};

// Date Utilities
export const DateUtils = {
  /**
   * Format date to readable string
   * @param {Date} date - Date object
   * @returns {string}
   */
  formatDate: (date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  },

  /**
   * Get relative time string
   * @param {Date} date - Date object
   * @returns {string}
   */
  getRelativeTime: (date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return DateUtils.formatDate(date);
  }
};

// API Utilities
export const API = {
  /**
   * Make HTTP request
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise}
   */
  request: async (url, options = {}) => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  },

  /**
   * GET request
   * @param {string} url - Request URL
   * @returns {Promise}
   */
  get: (url) => API.request(url, { method: 'GET' }),

  /**
   * POST request
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @returns {Promise}
   */
  post: (url, data) => API.request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  }),

  /**
   * PUT request
   * @param {string} url - Request URL
   * @param {Object} data - Request data
   * @returns {Promise}
   */
  put: (url, data) => API.request(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  }),

  /**
   * DELETE request
   * @param {string} url - Request URL
   * @returns {Promise}
   */
  delete: (url) => API.request(url, { method: 'DELETE' })
};

// Debounce utility
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle utility
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
