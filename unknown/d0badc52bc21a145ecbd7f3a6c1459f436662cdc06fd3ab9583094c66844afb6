
import React from 'react';
import { MarketPlayer } from '../types';

interface MarketPlayerCardProps {
  player: MarketPlayer;
}

const MarketPlayerCard: React.FC<MarketPlayerCardProps> = ({ player }) => {
  return (
    <div className="bg-secondary-50 shadow-lg rounded-xl overflow-hidden transform hover:scale-105 transition-transform duration-300 ease-in-out">
      <div className="p-6">
        <h3 className="text-xl font-semibold text-secondary-700 mb-2">{player.name}</h3>
        <p className="text-gray-600 text-sm">{player.focus}</p>
      </div>
    </div>
  );
};

export default MarketPlayerCard;
