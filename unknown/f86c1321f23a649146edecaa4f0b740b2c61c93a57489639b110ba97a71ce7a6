
import React from 'react';
import { VitalSignCard } from './VitalSignCard';
import { ECGDisplay } from './ECGDisplay';
import type { VitalSigns, ECGPoint } from '../types';
import { IconHeart, IconLungs, IconThermometer, IconGauge, IconSaturation } from '../constants';

interface PatientMonitorProps {
  vitalSigns: VitalSigns;
  ecgWaveform: ECGPoint[];
  ecgDescription: string;
}

export const PatientMonitor: React.FC<PatientMonitorProps> = ({ vitalSigns, ecgWaveform, ecgDescription }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 md:gap-6">
        <VitalSignCard
          label="Heart Rate"
          value={vitalSigns.heartRate}
          unit="bpm"
          icon={<IconHeart className="w-full h-full" />}
          colorClass={vitalSigns.heartRate > 100 || vitalSigns.heartRate < 60 ? 'text-orange-500' : 'text-red-500'}
        />
        <VitalSignCard
          label="SpO2"
          value={vitalSigns.spo2}
          unit="%"
          icon={<IconSaturation className="w-full h-full" />}
          colorClass={vitalSigns.spo2 < 94 ? 'text-orange-500' : 'text-sky-500'}
        />
        <VitalSignCard
          label="Blood Pressure"
          value={`${vitalSigns.systolicBP}/${vitalSigns.diastolicBP}`}
          unit="mmHg"
          icon={<IconGauge className="w-full h-full" />}
          colorClass={vitalSigns.systolicBP > 140 || vitalSigns.diastolicBP > 90 ? 'text-orange-500' : 'text-purple-500'}
        />
        <VitalSignCard
          label="Respiratory Rate"
          value={vitalSigns.respiratoryRate}
          unit="breaths/min"
          icon={<IconLungs className="w-full h-full" />}
          colorClass={vitalSigns.respiratoryRate > 20 || vitalSigns.respiratoryRate < 12 ? 'text-orange-500' : 'text-green-500'}
        />
        <VitalSignCard
          label="Temperature"
          value={vitalSigns.temperature.toFixed(1)}
          unit="°C"
          icon={<IconThermometer className="w-full h-full" />}
          colorClass={vitalSigns.temperature > 37.5 || vitalSigns.temperature < 36.0 ? 'text-orange-500' : 'text-yellow-500'}
        />
      </div>
      <ECGDisplay waveform={ecgWaveform} description={ecgDescription} />
    </div>
  );
};
