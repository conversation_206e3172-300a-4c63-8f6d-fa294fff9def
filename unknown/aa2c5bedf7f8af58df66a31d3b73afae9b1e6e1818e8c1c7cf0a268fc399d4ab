
import React from 'react';

interface ScenarioSelectorProps {
  scenarios: string[];
  selectedScenario: string;
  onScenarioChange: (scenario: string) => void;
  isLoading: boolean;
}

export const ScenarioSelector: React.FC<ScenarioSelectorProps> = ({
  scenarios,
  selectedScenario,
  onScenarioChange,
  isLoading,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <label htmlFor="scenario-select" className="block text-sm font-medium text-slate-700 mb-2">
        Select Patient Scenario:
      </label>
      <select
        id="scenario-select"
        value={selectedScenario}
        onChange={(e) => onScenarioChange(e.target.value)}
        disabled={isLoading}
        className="block w-full pl-3 pr-10 py-2 text-base border-slate-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {scenarios.map((scenario) => (
          <option key={scenario} value={scenario}>
            {scenario}
          </option>
        ))}
      </select>
      {isLoading && <p className="text-xs text-blue-600 mt-2 animate-pulse">Loading new scenario data...</p>}
    </div>
  );
};
