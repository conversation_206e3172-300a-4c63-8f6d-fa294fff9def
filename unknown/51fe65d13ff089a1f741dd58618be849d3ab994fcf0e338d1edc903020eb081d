import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';

interface Subject {
  id: string;
  name: string;
  description: string;
  detailedDescription: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced';
  icon: string;
  modules: CourseModule[];
  prerequisites?: string[];
  learningOutcomes: string[];
  credits: number;
  practicalApplications: string[];
  industryRelevance: string;
  assessmentMethods: string[];
  careerPaths: string[];
}

interface CourseModule {
  title: string;
  description: string;
  duration: string;
  topics: string[];
  practicalExercises: string[];
  assessments: string[];
}

interface Division {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  subjects: Subject[];
}

const SubjectCategoriesPage: React.FC = () => {
  const [selectedDivision, setSelectedDivision] = useState<string | null>(null);
  const [expandedCourse, setExpandedCourse] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();

  const divisions: Division[] = [
    {
      id: 'biomedical-imaging',
      title: 'Biomedical Imaging Instrumentation Technology',
      description: 'Advanced imaging technologies and instrumentation used in medical diagnosis and research.',
      color: 'from-blue-500 to-blue-700',
      icon: '🔬',
      subjects: [
        {
          id: 'mri-fundamentals',
          name: 'MRI Fundamentals',
          description: 'Comprehensive study of Magnetic Resonance Imaging principles, physics, and clinical applications.',
          detailedDescription: 'This course provides an in-depth understanding of MRI technology, from basic nuclear magnetic resonance principles to advanced imaging techniques. Students will learn about magnetic field interactions, pulse sequence design, image reconstruction, and safety protocols essential for MRI operation in clinical and research environments.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧲',
          credits: 3,
          modules: [
            {
              title: 'MRI Physics and Magnetic Field Theory',
              description: 'Fundamental principles of nuclear magnetic resonance and magnetic field interactions',
              duration: '1.5 weeks',
              topics: [
                'Nuclear spin and magnetic moments',
                'Larmor frequency and resonance conditions',
                'T1 and T2 relaxation mechanisms',
                'Magnetic field homogeneity and shimming',
                'Gradient coil systems and spatial encoding'
              ],
              practicalExercises: [
                'Calculate resonance frequencies for different field strengths',
                'Analyze relaxation curves and time constants',
                'Design gradient encoding schemes'
              ],
              assessments: ['Problem sets', 'Lab reports', 'Midterm exam']
            },
            {
              title: 'Pulse Sequences and Signal Generation',
              description: 'Design and analysis of MRI pulse sequences for different imaging applications',
              duration: '1.5 weeks',
              topics: [
                'Spin echo and gradient echo sequences',
                'Inversion recovery and STIR sequences',
                'Fast imaging techniques (EPI, RARE)',
                'Parallel imaging and acceleration methods',
                'Functional MRI sequences'
              ],
              practicalExercises: [
                'Design pulse sequences for specific contrast',
                'Optimize sequence parameters for image quality',
                'Implement parallel imaging reconstruction'
              ],
              assessments: ['Sequence design project', 'Practical exam']
            },
            {
              title: 'Image Reconstruction Algorithms',
              description: 'Mathematical foundations of MRI image reconstruction and processing',
              duration: '1.5 weeks',
              topics: [
                'Fourier transform and k-space concepts',
                'Gridding and regridding algorithms',
                'Parallel imaging reconstruction (SENSE, GRAPPA)',
                'Compressed sensing techniques',
                'Motion correction algorithms'
              ],
              practicalExercises: [
                'Implement basic reconstruction algorithms',
                'Apply parallel imaging techniques',
                'Develop motion correction methods'
              ],
              assessments: ['Programming assignments', 'Algorithm implementation']
            },
            {
              title: 'Contrast Mechanisms and Tissue Properties',
              description: 'Understanding tissue contrast and optimization for clinical diagnosis',
              duration: '1.5 weeks',
              topics: [
                'T1, T2, and proton density contrast',
                'Contrast agent mechanisms and applications',
                'Tissue characterization techniques',
                'Quantitative MRI methods',
                'Diffusion and perfusion imaging'
              ],
              practicalExercises: [
                'Optimize contrast for different tissues',
                'Analyze contrast agent kinetics',
                'Perform quantitative measurements'
              ],
              assessments: ['Case study analysis', 'Contrast optimization project']
            },
            {
              title: 'MRI Safety and Quality Control',
              description: 'Comprehensive safety protocols and quality assurance procedures',
              duration: '1 week',
              topics: [
                'Static magnetic field safety zones',
                'RF heating and SAR limitations',
                'Implant and device compatibility',
                'Emergency procedures and quench protocols',
                'Quality control testing procedures'
              ],
              practicalExercises: [
                'Conduct safety zone calculations',
                'Perform quality control tests',
                'Develop emergency response protocols'
              ],
              assessments: ['Safety certification exam', 'QC procedure demonstration']
            },
            {
              title: 'Clinical Applications and Case Studies',
              description: 'Real-world applications of MRI in clinical practice and research',
              duration: '1 week',
              topics: [
                'Neuroimaging applications and protocols',
                'Cardiac and vascular imaging',
                'Musculoskeletal imaging techniques',
                'Abdominal and pelvic imaging',
                'Research applications and emerging techniques'
              ],
              practicalExercises: [
                'Analyze clinical cases',
                'Design imaging protocols',
                'Evaluate image quality and diagnostic value'
              ],
              assessments: ['Case presentation', 'Protocol design project', 'Final exam']
            }
          ],
          prerequisites: ['Basic Physics', 'Mathematics', 'Introduction to Medical Imaging'],
          learningOutcomes: [
            'Demonstrate comprehensive understanding of MRI physics principles and their clinical applications',
            'Design and optimize pulse sequences for specific imaging requirements and tissue contrast',
            'Implement and troubleshoot image reconstruction algorithms including parallel imaging techniques',
            'Apply rigorous safety protocols and quality control procedures in MRI environments',
            'Evaluate and interpret MRI images for clinical and research applications',
            'Analyze the relationship between sequence parameters and image characteristics'
          ],
          practicalApplications: [
            'Clinical MRI technologist and physicist roles',
            'MRI research and protocol development',
            'Medical device design and testing',
            'Quality assurance and safety compliance',
            'Advanced imaging technique development',
            'Radiological consultation and interpretation'
          ],
          industryRelevance: 'MRI is a cornerstone technology in modern healthcare, with the global MRI market valued at over $8 billion. This course prepares students for careers in hospitals, imaging centers, research institutions, and medical device companies. Skills learned are directly applicable to roles in clinical imaging, research, and medical technology development.',
          assessmentMethods: [
            'Written examinations (40%)',
            'Practical laboratory assignments (25%)',
            'Project-based assessments (20%)',
            'Case study presentations (10%)',
            'Safety certification (5%)'
          ],
          careerPaths: [
            'MRI Technologist',
            'Medical Physics Specialist',
            'Biomedical Engineer - Imaging Systems',
            'Clinical Research Coordinator',
            'Medical Device Applications Specialist',
            'Radiological Safety Officer'
          ]
        },
        {
          id: 'ct-imaging',
          name: 'CT Imaging Technology',
          description: 'Comprehensive study of Computed Tomography systems, image reconstruction, and quality control.',
          detailedDescription: 'This course covers the complete spectrum of CT imaging technology, from fundamental X-ray physics to advanced reconstruction algorithms and clinical protocols. Students will gain hands-on experience with CT systems, learn optimization techniques, and understand radiation safety principles essential for modern healthcare imaging.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '💻',
          credits: 3,
          modules: [
            {
              title: 'X-ray Physics and Attenuation',
              description: 'Fundamental principles of X-ray generation and tissue interaction',
              duration: '1 week',
              topics: [
                'X-ray tube design and operation',
                'Beam filtration and collimation',
                'Attenuation coefficients and Hounsfield units',
                'Beam hardening effects',
                'Scatter radiation characteristics'
              ],
              practicalExercises: [
                'Calculate attenuation values for different tissues',
                'Analyze beam quality and filtration effects',
                'Measure and correct for beam hardening'
              ],
              assessments: ['Physics problem sets', 'Lab measurements']
            },
            {
              title: 'CT Scanner Components and Design',
              description: 'Hardware components and system architecture of modern CT scanners',
              duration: '1 week',
              topics: [
                'Gantry design and rotation mechanisms',
                'Detector arrays and data acquisition',
                'Slip ring technology',
                'Multi-slice and cone beam geometry',
                'Dual-energy CT systems'
              ],
              practicalExercises: [
                'Analyze scanner specifications',
                'Compare different detector technologies',
                'Evaluate system performance metrics'
              ],
              assessments: ['Technical reports', 'System analysis project']
            },
            {
              title: 'Image Reconstruction Techniques',
              description: 'Mathematical algorithms for CT image reconstruction',
              duration: '1.5 weeks',
              topics: [
                'Filtered back-projection algorithm',
                'Iterative reconstruction methods',
                'Cone beam reconstruction',
                'Metal artifact reduction',
                'AI-enhanced reconstruction'
              ],
              practicalExercises: [
                'Implement basic reconstruction algorithms',
                'Compare reconstruction methods',
                'Optimize reconstruction parameters'
              ],
              assessments: ['Programming assignments', 'Algorithm comparison']
            },
            {
              title: 'Contrast Agents and Protocols',
              description: 'Clinical protocols and contrast enhancement techniques',
              duration: '1 week',
              topics: [
                'Iodinated contrast agents',
                'Injection protocols and timing',
                'Contrast enhancement patterns',
                'Adverse reactions and management',
                'Dual-energy contrast applications'
              ],
              practicalExercises: [
                'Design contrast protocols',
                'Analyze enhancement patterns',
                'Calculate contrast volumes and flow rates'
              ],
              assessments: ['Protocol design project', 'Case studies']
            },
            {
              title: 'Radiation Dose and Safety',
              description: 'Radiation protection and dose optimization in CT',
              duration: '1 week',
              topics: [
                'CT dose descriptors (CTDI, DLP)',
                'Dose reduction techniques',
                'Pediatric dose considerations',
                'Radiation protection principles',
                'Dose monitoring and reporting'
              ],
              practicalExercises: [
                'Calculate radiation doses',
                'Implement dose reduction strategies',
                'Perform dose audits'
              ],
              assessments: ['Dose calculation exercises', 'Safety protocol exam']
            },
            {
              title: 'Quality Assurance and Maintenance',
              description: 'QA procedures and preventive maintenance protocols',
              duration: '0.5 weeks',
              topics: [
                'Daily, weekly, and monthly QA tests',
                'Image quality metrics',
                'Preventive maintenance schedules',
                'Troubleshooting common problems',
                'Regulatory compliance'
              ],
              practicalExercises: [
                'Perform QA measurements',
                'Analyze image quality data',
                'Develop maintenance protocols'
              ],
              assessments: ['QA practical exam', 'Maintenance planning project']
            }
          ],
          prerequisites: ['Radiographic Physics', 'Basic Anatomy', 'Mathematics'],
          learningOutcomes: [
            'Demonstrate comprehensive understanding of CT physics and technology',
            'Optimize scanning parameters for various clinical examinations',
            'Implement effective radiation safety and dose reduction strategies',
            'Perform systematic quality control and maintenance procedures',
            'Design appropriate contrast protocols for different clinical indications',
            'Troubleshoot technical problems and optimize system performance'
          ],
          practicalApplications: [
            'Clinical CT technologist and supervisor roles',
            'Medical physics specialist in diagnostic imaging',
            'CT system applications and training specialist',
            'Quality assurance coordinator',
            'Radiation safety officer',
            'Clinical research in CT imaging'
          ],
          industryRelevance: 'CT imaging is one of the most widely used diagnostic modalities, with over 80 million CT scans performed annually in the US alone. The global CT market exceeds $6 billion, with continuous technological advancement in AI reconstruction, dose reduction, and spectral imaging. This course prepares students for high-demand careers in hospitals, imaging centers, and medical device companies.',
          assessmentMethods: [
            'Written examinations (35%)',
            'Practical laboratory work (30%)',
            'Technical projects (20%)',
            'Case study analysis (10%)',
            'Safety certification (5%)'
          ],
          careerPaths: [
            'CT Technologist',
            'Senior CT Technologist/Supervisor',
            'Medical Physics Specialist',
            'CT Applications Specialist',
            'Quality Assurance Coordinator',
            'Clinical Research Coordinator'
          ]
        },
        {
          id: 'ultrasound-physics',
          name: 'Ultrasound Physics & Instrumentation',
          description: 'Ultrasound wave physics, transducers, and imaging system components.',
          duration: '7 weeks',
          level: 'Beginner',
          icon: '🌊',
          credits: 3,
          modules: [
            'Acoustic Wave Properties and Propagation',
            'Transducer Design and Beam Characteristics',
            'Doppler Effect and Flow Measurement',
            'Image Formation and Display',
            'Artifacts and Image Optimization',
            'Safety Considerations and Bioeffects'
          ],
          learningOutcomes: [
            'Understand ultrasound wave physics',
            'Operate ultrasound equipment effectively',
            'Recognize and minimize imaging artifacts',
            'Apply safety guidelines for ultrasound use'
          ]
        },
        {
          id: 'nuclear-medicine',
          name: 'Nuclear Medicine Imaging',
          description: 'Radiopharmaceuticals, gamma cameras, and SPECT/PET imaging systems.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '☢️',
          credits: 4,
          modules: [
            'Radioactive Decay and Nuclear Physics',
            'Radiopharmaceuticals and Tracers',
            'Gamma Camera and Detector Systems',
            'SPECT Imaging Principles and Reconstruction',
            'PET Imaging Technology and Applications',
            'Radiation Safety and Regulatory Compliance',
            'Clinical Nuclear Medicine Procedures',
            'Quality Control and Performance Testing'
          ],
          prerequisites: ['Nuclear Physics', 'Radiation Safety', 'Medical Imaging Fundamentals'],
          learningOutcomes: [
            'Understand nuclear decay processes and radiation interactions',
            'Analyze radiopharmaceutical properties and biodistribution',
            'Operate nuclear medicine imaging equipment',
            'Implement comprehensive radiation safety programs',
            'Interpret nuclear medicine studies'
          ]
        },
        {
          id: 'digital-radiography',
          name: 'Digital Radiography',
          description: 'Digital X-ray systems, image processing, and PACS integration.',
          duration: '5 weeks',
          level: 'Beginner',
          icon: '📷',
          credits: 2,
          modules: [
            'Digital Detector Technologies',
            'Image Acquisition and Processing',
            'DICOM Standards and Protocols',
            'PACS and RIS Integration',
            'Image Quality and Optimization',
            'Workflow and System Management'
          ],
          learningOutcomes: [
            'Compare different digital detector technologies',
            'Optimize image acquisition parameters',
            'Understand DICOM and healthcare informatics',
            'Manage digital imaging workflows'
          ]
        },
        {
          id: 'image-processing',
          name: 'Medical Image Processing',
          description: 'Image enhancement, segmentation, and analysis algorithms.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🖼️',
          credits: 4,
          modules: [
            'Digital Image Fundamentals',
            'Image Enhancement Techniques',
            'Filtering and Noise Reduction',
            'Image Segmentation Methods',
            'Feature Extraction and Analysis',
            'Machine Learning in Medical Imaging',
            'Image Registration and Fusion',
            'Quantitative Image Analysis'
          ],
          prerequisites: ['Programming', 'Mathematics', 'Medical Imaging'],
          learningOutcomes: [
            'Apply image processing algorithms to medical images',
            'Develop automated analysis tools',
            'Implement machine learning techniques',
            'Perform quantitative image analysis'
          ]
        }
      ]
    },
    {
      id: 'electrophysiological',
      title: 'Electrophysiological Instrumentation and Measurements',
      description: 'Electrical activity measurement and analysis in biological systems.',
      color: 'from-green-500 to-green-700',
      icon: '⚡',
      subjects: [
        {
          id: 'ecg-fundamentals',
          name: 'ECG Fundamentals',
          description: 'Electrocardiography principles, lead systems, and signal acquisition.',
          duration: '6 weeks',
          level: 'Beginner',
          icon: '💓',
          credits: 3,
          modules: [
            'Cardiac Anatomy and Electrophysiology',
            'ECG Lead Systems and Electrode Placement',
            'Signal Acquisition and Amplification',
            'Normal ECG Waveforms and Intervals',
            'Arrhythmia Recognition and Analysis',
            'ECG Equipment and Quality Control'
          ],
          learningOutcomes: [
            'Understand cardiac electrical conduction system',
            'Perform proper ECG electrode placement',
            'Recognize normal and abnormal ECG patterns',
            'Operate ECG equipment safely and effectively'
          ]
        },
        {
          id: 'eeg-analysis',
          name: 'EEG Signal Analysis',
          description: 'Electroencephalography recording, artifact removal, and interpretation.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🧠',
          credits: 4,
          modules: [
            'Neuroanatomy and Brain Electrical Activity',
            'EEG Electrode Systems and Montages',
            'Signal Processing and Filtering',
            'Artifact Recognition and Removal',
            'Frequency Domain Analysis',
            'Event-Related Potentials',
            'Sleep Studies and Polysomnography',
            'Clinical EEG Interpretation'
          ],
          prerequisites: ['Neuroanatomy', 'Signal Processing Basics'],
          learningOutcomes: [
            'Understand brain electrical activity patterns',
            'Apply advanced signal processing techniques',
            'Identify and remove various artifacts',
            'Interpret clinical EEG recordings'
          ]
        },
        {
          id: 'emg-systems',
          name: 'EMG Measurement Systems',
          description: 'Electromyography instrumentation and muscle activity analysis.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '💪',
          credits: 3,
          modules: [
            'Muscle Physiology and Motor Units',
            'Surface and Intramuscular EMG',
            'EMG Signal Characteristics',
            'Amplification and Filtering Systems',
            'Motion Artifact and Noise Reduction',
            'EMG Analysis Techniques',
            'Clinical and Research Applications'
          ],
          prerequisites: ['Muscle Physiology', 'Basic Electronics'],
          learningOutcomes: [
            'Understand muscle electrical activity',
            'Design EMG measurement systems',
            'Analyze EMG signals quantitatively',
            'Apply EMG in clinical and research settings'
          ]
        },
        {
          id: 'bioamplifiers',
          name: 'Biomedical Amplifiers',
          description: 'Design and characteristics of amplifiers for biological signals.',
          duration: '5 weeks',
          level: 'Advanced',
          icon: '📡',
          credits: 3,
          modules: [
            'Operational Amplifier Fundamentals',
            'Differential Amplifier Design',
            'Instrumentation Amplifiers',
            'Isolation Amplifiers and Safety',
            'Noise Analysis and Reduction',
            'Frequency Response and Bandwidth'
          ],
          prerequisites: ['Electronics', 'Circuit Analysis', 'Signal Processing'],
          learningOutcomes: [
            'Design biomedical amplifier circuits',
            'Analyze noise sources and mitigation strategies',
            'Implement electrical safety measures',
            'Optimize amplifier performance for specific applications'
          ]
        },
        {
          id: 'signal-conditioning',
          name: 'Signal Conditioning',
          description: 'Filtering, amplification, and noise reduction in biomedical signals.',
          duration: '6 weeks',
          level: 'Intermediate',
          icon: '🔧',
          credits: 3,
          modules: [
            'Analog Filter Design',
            'Digital Signal Processing',
            'Sampling and Quantization',
            'Noise Sources and Characteristics',
            'Adaptive Filtering Techniques',
            'Real-time Signal Processing'
          ],
          prerequisites: ['Signal Processing', 'Mathematics'],
          learningOutcomes: [
            'Design appropriate filtering systems',
            'Implement digital signal processing algorithms',
            'Optimize signal-to-noise ratio',
            'Develop real-time processing solutions'
          ]
        },
        {
          id: 'neural-interfaces',
          name: 'Neural Interface Technology',
          description: 'Brain-computer interfaces and neural signal processing.',
          duration: '12 weeks',
          level: 'Advanced',
          icon: '🔌',
          credits: 5,
          modules: [
            'Neuroscience Fundamentals',
            'Neural Signal Acquisition',
            'Microelectrode Arrays and Implants',
            'Signal Processing for Neural Data',
            'Machine Learning for BCI',
            'Closed-loop Control Systems',
            'Ethical and Safety Considerations',
            'Clinical Applications and Case Studies'
          ],
          prerequisites: ['Neuroscience', 'Signal Processing', 'Programming', 'Machine Learning'],
          learningOutcomes: [
            'Understand neural signal characteristics',
            'Design brain-computer interface systems',
            'Implement machine learning algorithms for neural decoding',
            'Address ethical and safety considerations in neural interfaces'
          ]
        }
      ]
    },
    {
      id: 'biomechanics-rehab',
      title: 'Biomechanics and Rehabilitation Engineering',
      description: 'Mechanical principles applied to biological systems and rehabilitation technologies.',
      color: 'from-purple-500 to-purple-700',
      icon: '🦴',
      subjects: [
        {
          id: 'gait-analysis',
          name: 'Gait Analysis',
          description: 'Motion capture systems and biomechanical analysis of human walking.',
          duration: '8 weeks',
          level: 'Intermediate',
          icon: '🚶',
          credits: 4,
          modules: [
            'Human Locomotion Fundamentals',
            'Motion Capture Technologies',
            'Force Plate and Pressure Systems',
            'Kinematic and Kinetic Analysis',
            'Gait Cycle and Parameters',
            'Pathological Gait Patterns',
            'Data Processing and Interpretation',
            'Clinical Applications'
          ],
          prerequisites: ['Biomechanics', 'Anatomy', 'Physics'],
          learningOutcomes: [
            'Understand normal and pathological gait patterns',
            'Operate motion capture and force measurement systems',
            'Analyze kinematic and kinetic data',
            'Apply gait analysis in clinical settings'
          ]
        },
        {
          id: 'prosthetics-design',
          name: 'Prosthetics Design',
          description: 'Design principles and materials for artificial limbs and devices.',
          duration: '10 weeks',
          level: 'Advanced',
          icon: '🦿',
          credits: 4,
          modules: [
            'Amputation Levels and Residual Limb Anatomy',
            'Biomechanical Principles of Prosthetics',
            'Materials Science for Prosthetics',
            'Socket Design and Fitting',
            'Mechanical and Microprocessor Components',
            'Control Systems and User Interfaces',
            'Manufacturing and Fabrication',
            'Clinical Evaluation and Outcomes'
          ],
          prerequisites: ['Biomechanics', 'Materials Science', 'Anatomy'],
          learningOutcomes: [
            'Design prosthetic devices for different amputation levels',
            'Select appropriate materials and components',
            'Understand user needs and functional requirements',
            'Evaluate prosthetic performance and outcomes'
          ]
        },
        {
          id: 'orthotics-systems',
          name: 'Orthotic Systems',
          description: 'Supportive devices for musculoskeletal disorders and rehabilitation.',
          duration: '7 weeks',
          level: 'Intermediate',
          icon: '🦵',
          credits: 3,
          modules: [
            'Musculoskeletal Anatomy and Pathology',
            'Biomechanical Principles of Orthotics',
            'Materials and Manufacturing Processes',
            'Spinal Orthoses Design',
            'Lower Limb Orthoses',
            'Upper Limb Orthoses',
            'Pediatric Considerations'
          ],
          prerequisites: ['Anatomy', 'Biomechanics'],
          learningOutcomes: [
            'Understand orthotic principles and applications',
            'Design orthotic devices for various conditions',
            'Select appropriate materials and fabrication methods',
            'Consider patient-specific factors in orthotic design'
          ]
        },
        {
          id: 'biomaterials',
          name: 'Biomaterials Engineering',
          description: 'Materials science applied to medical devices and implants.',
          duration: '9 weeks',
          level: 'Advanced',
          icon: '🧪',
          credits: 4,
          modules: [
            'Materials Science Fundamentals',
            'Biocompatibility and Bioactivity',
            'Metallic Biomaterials',
            'Ceramic and Composite Materials',
            'Polymeric Biomaterials',
            'Surface Modification Techniques',
            'Degradation and Corrosion',
            'Regulatory and Testing Standards'
          ],
          prerequisites: ['Materials Science', 'Chemistry', 'Biology'],
          learningOutcomes: [
            'Understand material-tissue interactions',
            'Select appropriate materials for medical applications',
            'Design biocompatible material systems',
            'Evaluate material performance and safety'
          ]
        },
        {
          id: 'rehabilitation-robotics',
          name: 'Rehabilitation Robotics',
          description: 'Robotic systems for physical therapy and motor recovery.',
          duration: '11 weeks',
          level: 'Advanced',
          icon: '🤖',
          credits: 5,
          modules: [
            'Robotics Fundamentals',
            'Human Motor Control and Learning',
            'Rehabilitation Principles',
            'Exoskeleton Design and Control',
            'End-effector Based Robots',
            'Virtual Reality Integration',
            'Adaptive Control Systems',
            'Clinical Validation and Outcomes',
            'Emerging Technologies'
          ],
          prerequisites: ['Robotics', 'Control Systems', 'Biomechanics', 'Programming'],
          learningOutcomes: [
            'Design rehabilitation robotic systems',
            'Implement adaptive control algorithms',
            'Integrate virtual reality with robotic therapy',
            'Evaluate clinical effectiveness of robotic interventions'
          ]
        },
        {
          id: 'biomechanical-modeling',
          name: 'Biomechanical Modeling',
          description: 'Mathematical models of human movement and tissue mechanics.',
          duration: '8 weeks',
          level: 'Advanced',
          icon: '📊',
          credits: 4,
          modules: [
            'Mathematical Modeling Fundamentals',
            'Musculoskeletal System Modeling',
            'Finite Element Analysis',
            'Multibody Dynamics',
            'Tissue Mechanics and Constitutive Models',
            'Model Validation and Verification',
            'Computational Tools and Software',
            'Clinical Applications'
          ],
          prerequisites: ['Mathematics', 'Biomechanics', 'Programming', 'Physics'],
          learningOutcomes: [
            'Develop mathematical models of biological systems',
            'Apply finite element analysis to biomechanical problems',
            'Validate computational models with experimental data',
            'Use modeling for clinical decision support'
          ]
        }
      ]
    }
  ];

  // Handle URL parameters for direct specialization selection
  useEffect(() => {
    const specialization = searchParams.get('specialization');
    if (specialization && divisions.find(div => div.id === specialization)) {
      setSelectedDivision(specialization);
    }
  }, [searchParams, divisions]);

  // Function to handle division selection and update URL
  const handleDivisionSelection = (divisionId: string) => {
    const newSelection = selectedDivision === divisionId ? null : divisionId;
    setSelectedDivision(newSelection);

    // Update URL parameters
    if (newSelection) {
      setSearchParams({ specialization: newSelection });
    } else {
      setSearchParams({});
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Biomedical Engineering Subject Categories
          </h1>
          <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Explore comprehensive courses across three core divisions of biomedical engineering
          </p>
          <div className="mt-8">
            <Link
              to="/"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </section>

      {/* Division Overview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Specialization
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select a division to explore detailed courses and learning paths
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {divisions.map((division) => (
              <div
                key={division.id}
                className={`relative overflow-hidden rounded-2xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-105 ${
                  selectedDivision === division.id ? 'ring-4 ring-blue-500' : ''
                }`}
                onClick={() => handleDivisionSelection(division.id)}
              >
                <div className={`bg-gradient-to-br ${division.color} p-8 text-white`}>
                  <div className="text-4xl mb-4">{division.icon}</div>
                  <h3 className="text-xl font-bold mb-3">{division.title}</h3>
                  <p className="text-blue-100 leading-relaxed">{division.description}</p>
                  <div className="mt-6">
                    <span className="inline-flex items-center text-sm font-medium">
                      {division.subjects.length} Courses Available
                      <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Selected Division Details */}
          {selectedDivision && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              {divisions
                .filter(division => division.id === selectedDivision)
                .map(division => (
                  <div key={division.id}>
                    <div className="flex items-center mb-8">
                      <div className="text-3xl mr-4">{division.icon}</div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{division.title}</h3>
                        <p className="text-gray-600">{division.description}</p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {division.subjects.map((subject) => (
                        <div
                          key={subject.id}
                          className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-blue-300"
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="text-2xl">{subject.icon}</div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(subject.level)}`}>
                              {subject.level}
                            </span>
                          </div>

                          <h4 className="text-lg font-semibold text-gray-900 mb-2">
                            {subject.name}
                          </h4>

                          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                            {subject.description}
                          </p>

                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-500">
                                📅 {subject.duration}
                              </span>
                              <span className="text-sm font-medium text-blue-600">
                                {subject.credits} Credits
                              </span>
                            </div>

                            {subject.prerequisites && (
                              <div className="mb-3">
                                <p className="text-xs font-medium text-gray-700 mb-1">Prerequisites:</p>
                                <div className="flex flex-wrap gap-1">
                                  {subject.prerequisites.map((prereq, index) => (
                                    <span key={index} className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                                      {prereq}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}

                            <div className="mb-3">
                              <p className="text-xs font-medium text-gray-700 mb-1">Course Modules ({subject.modules.length}):</p>
                              <div className="max-h-20 overflow-y-auto">
                                <ul className="text-xs text-gray-600 space-y-1">
                                  {subject.modules.slice(0, 3).map((module, index) => (
                                    <li key={index} className="flex items-start">
                                      <span className="text-blue-500 mr-1">•</span>
                                      {typeof module === 'string' ? module : module.title}
                                    </li>
                                  ))}
                                  {subject.modules.length > 3 && (
                                    <li className="text-gray-500 italic">
                                      +{subject.modules.length - 3} more modules...
                                    </li>
                                  )}
                                </ul>
                              </div>
                            </div>

                            <div className="mb-3">
                              <p className="text-xs font-medium text-gray-700 mb-1">Industry Applications:</p>
                              <div className="max-h-16 overflow-y-auto">
                                <ul className="text-xs text-gray-600 space-y-1">
                                  {subject.practicalApplications?.slice(0, 2).map((application, index) => (
                                    <li key={index} className="flex items-start">
                                      <span className="text-green-500 mr-1">✓</span>
                                      {application}
                                    </li>
                                  ))}
                                  {subject.practicalApplications && subject.practicalApplications.length > 2 && (
                                    <li className="text-gray-500 italic">
                                      +{subject.practicalApplications.length - 2} more applications...
                                    </li>
                                  )}
                                </ul>
                              </div>
                            </div>

                            <button
                              type="button"
                              onClick={() => setExpandedCourse(expandedCourse === subject.id ? null : subject.id)}
                              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded transition-colors"
                            >
                              {expandedCourse === subject.id ? 'Hide Details' : 'View Full Course Details'}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
            </div>

            {/* Expanded Course Details */}
            {expandedCourse && (
              <div className="mt-8 bg-white rounded-2xl shadow-xl p-8 border-l-4 border-blue-500">
                {divisions
                  .filter(division => division.id === selectedDivision)
                  .map(division =>
                    division.subjects
                      .filter(subject => subject.id === expandedCourse)
                      .map(subject => (
                        <div key={subject.id} className="space-y-8">
                          {/* Course Header */}
                          <div className="border-b border-gray-200 pb-6">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center space-x-4">
                                <div className="text-4xl">{subject.icon}</div>
                                <div>
                                  <h3 className="text-3xl font-bold text-gray-900 mb-2">{subject.name}</h3>
                                  <p className="text-lg text-gray-600 mb-4">{subject.detailedDescription}</p>
                                  <div className="flex items-center space-x-6 text-sm">
                                    <span className="flex items-center">
                                      <span className="font-medium text-gray-700">Duration:</span>
                                      <span className="ml-2 text-gray-600">{subject.duration}</span>
                                    </span>
                                    <span className="flex items-center">
                                      <span className="font-medium text-gray-700">Credits:</span>
                                      <span className="ml-2 text-blue-600 font-semibold">{subject.credits}</span>
                                    </span>
                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getLevelColor(subject.level)}`}>
                                      {subject.level}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <button
                                type="button"
                                onClick={() => setExpandedCourse(null)}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                              >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>

                          {/* Course Content Grid */}
                          <div className="grid lg:grid-cols-3 gap-8">
                            {/* Left Column - Modules */}
                            <div className="lg:col-span-2 space-y-6">
                              <div>
                                <h4 className="text-xl font-semibold text-gray-900 mb-4">Course Modules</h4>
                                <div className="space-y-4">
                                  {subject.modules.map((module, index) => (
                                    <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                      <div className="flex items-start justify-between mb-3">
                                        <h5 className="text-lg font-semibold text-gray-900">{typeof module === 'string' ? module : module.title}</h5>
                                        {typeof module !== 'string' && (
                                          <span className="text-sm text-gray-500 bg-white px-2 py-1 rounded">
                                            {module.duration}
                                          </span>
                                        )}
                                      </div>
                                      {typeof module !== 'string' && (
                                        <>
                                          <p className="text-gray-600 mb-4">{module.description}</p>

                                          <div className="grid md:grid-cols-2 gap-4">
                                            <div>
                                              <h6 className="font-medium text-gray-700 mb-2">Key Topics:</h6>
                                              <ul className="text-sm text-gray-600 space-y-1">
                                                {module.topics.map((topic, topicIndex) => (
                                                  <li key={topicIndex} className="flex items-start">
                                                    <span className="text-blue-500 mr-2">•</span>
                                                    {topic}
                                                  </li>
                                                ))}
                                              </ul>
                                            </div>

                                            <div>
                                              <h6 className="font-medium text-gray-700 mb-2">Practical Exercises:</h6>
                                              <ul className="text-sm text-gray-600 space-y-1">
                                                {module.practicalExercises.map((exercise, exerciseIndex) => (
                                                  <li key={exerciseIndex} className="flex items-start">
                                                    <span className="text-green-500 mr-2">✓</span>
                                                    {exercise}
                                                  </li>
                                                ))}
                                              </ul>
                                            </div>
                                          </div>

                                          <div className="mt-4 pt-4 border-t border-gray-200">
                                            <h6 className="font-medium text-gray-700 mb-2">Assessments:</h6>
                                            <div className="flex flex-wrap gap-2">
                                              {module.assessments.map((assessment, assessmentIndex) => (
                                                <span key={assessmentIndex} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                  {assessment}
                                                </span>
                                              ))}
                                            </div>
                                          </div>
                                        </>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* Right Column - Course Info */}
                            <div className="space-y-6">
                              {/* Prerequisites */}
                              {subject.prerequisites && (
                                <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                                  <h4 className="text-lg font-semibold text-gray-900 mb-3">Prerequisites</h4>
                                  <div className="space-y-2">
                                    {subject.prerequisites.map((prereq, index) => (
                                      <span key={index} className="inline-block bg-yellow-100 text-yellow-800 text-sm px-3 py-1 rounded-full mr-2 mb-2">
                                        {prereq}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Learning Outcomes */}
                              <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                                <h4 className="text-lg font-semibold text-gray-900 mb-3">Learning Outcomes</h4>
                                <ul className="space-y-2">
                                  {subject.learningOutcomes.map((outcome, index) => (
                                    <li key={index} className="flex items-start text-sm text-gray-700">
                                      <span className="text-green-500 mr-2 mt-1">✓</span>
                                      {outcome}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Career Paths */}
                              <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
                                <h4 className="text-lg font-semibold text-gray-900 mb-3">Career Paths</h4>
                                <ul className="space-y-2">
                                  {subject.careerPaths.map((career, index) => (
                                    <li key={index} className="flex items-start text-sm text-gray-700">
                                      <span className="text-purple-500 mr-2 mt-1">→</span>
                                      {career}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Assessment Methods */}
                              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                                <h4 className="text-lg font-semibold text-gray-900 mb-3">Assessment Methods</h4>
                                <ul className="space-y-2">
                                  {subject.assessmentMethods.map((method, index) => (
                                    <li key={index} className="text-sm text-gray-700">
                                      {method}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Industry Relevance */}
                              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                                <h4 className="text-lg font-semibold text-gray-900 mb-3">Industry Relevance</h4>
                                <p className="text-sm text-gray-700 leading-relaxed">
                                  {subject.industryRelevance}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                  )}
              </div>
            )}
          </div>

          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gray-900 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Start Your Learning Journey?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of biomedical engineering students advancing their careers
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/tracker"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Track Your Progress
            </Link>
            <Link
              to="/discover"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Discover More LMS
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SubjectCategoriesPage;
