
import React from 'react';

interface SubjectDivision {
  title: string;
  subjects: string[];
  colorClass: string; // For distinct visual styling of section headers
}

const biomedicalDivisions: SubjectDivision[] = [
  {
    title: 'Biomedical Imaging Instrumentation Technology',
    subjects: [
      'Physics of Medical Imaging (X-ray, CT, MRI, US, Nuclear)',
      'X-Ray Imaging and Computed Tomography (CT) Systems',
      'Magnetic Resonance Imaging (MRI) Principles and Instrumentation',
      'Ultrasound Imaging Systems and Applications',
      'Nuclear Medicine Instrumentation (PET, SPECT)',
      'Digital Image Processing for Medical Applications',
      'Optical Imaging and Microscopy in Biomedicine',
      'PACS and Medical Informatics in Imaging',
    ],
    colorClass: 'border-primary-500',
  },
  {
    title: 'Medical Electronics and Physiological Instrumentation Measurements',
    subjects: [
      'Biomedical Sensors, Transducers, and Electrodes',
      'Biopotential Amplifiers and Signal Conditioning Circuits',
      'Electrocardiography (ECG) Systems and Analysis',
      'Electroencephalography (EEG) and Electromyography (EMG) Instrumentation',
      'Patient Monitoring Systems (Vital Signs, Hemodynamics)',
      'Principles of Medical Device Design and Safety Standards (e.g. IEC 60601)',
      'Wearable Health Technology and Biosensors',
      'Microcontrollers in Medical Instrumentation',
    ],
    colorClass: 'border-secondary-500',
  },
  {
    title: 'Biomechanics and Rehabilitation Engineering',
    subjects: [
      'Statics and Dynamics in Biomechanics',
      'Mechanics of Biological Tissues (Bone, Cartilage, Soft Tissues)',
      'Fluid Biomechanics (Cardiovascular, Respiratory Systems)',
      'Kinesiology and Human Movement Analysis',
      'Orthopedic Biomechanics and Implant Design',
      'Prosthetics, Orthotics, and Assistive Devices',
      'Rehabilitation Robotics and Neural Engineering',
      'Biofabrication and Tissue Engineering Fundamentals',
    ],
    colorClass: 'border-green-500',
  },
];

const BiomedicalSubjectsPage: React.FC = () => {
  return (
    <div className="space-y-12">
      <section className="text-center py-8 bg-gradient-to-r from-primary-600 to-secondary-500 rounded-lg shadow-xl text-white">
        <h1 className="text-4xl font-bold mb-3">Core Biomedical Engineering Subjects</h1>
        <p className="text-lg opacity-90 max-w-3xl mx-auto">
          Explore foundational subjects across key specializations within Biomedical Engineering.
        </p>
      </section>

      {biomedicalDivisions.map((division, index) => (
        <section key={index} aria-labelledby={`division-title-${index}`}>
          <h2 
            id={`division-title-${index}`} 
            className={`text-2xl sm:text-3xl font-semibold text-gray-800 mb-6 pb-2 border-b-2 ${division.colorClass}`}
          >
            {division.title}
          </h2>
          <div className="bg-white shadow-lg rounded-lg p-6">
            <ul className="list-disc list-inside space-y-3 text-gray-700">
              {division.subjects.map((subject, subIndex) => (
                <li key={subIndex} className="text-base sm:text-lg leading-relaxed">
                  {subject}
                </li>
              ))}
            </ul>
          </div>
        </section>
      ))}
    </div>
  );
};

export default BiomedicalSubjectsPage;
