
import React, { useState, useEffect, useCallback } from 'react';
import { TrackedLMS, TrackedLMSStatus } from '../types';
import { LMS_TRACKER_STORAGE_KEY } from '../constants';
import Modal from '../components/Modal';
import PlusIcon from '../components/icons/PlusIcon';
import PencilIcon from '../components/icons/PencilIcon';
import TrashIcon from '../components/icons/TrashIcon';

const LMSTrackerPage: React.FC = () => {
  const [trackedLMSs, setTrackedLMSs] = useState<TrackedLMS[]>([]);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [currentLMS, setCurrentLMS] = useState<TrackedLMS | null>(null);
  const [lmsName, setLmsName] = useState<string>('');
  const [lmsNotes, setLmsNotes] = useState<string>('');
  const [lmsStatus, setLmsStatus] = useState<TrackedLMSStatus>(TrackedLMSStatus.RESEARCHING);

  useEffect(() => {
    const storedLMSs = localStorage.getItem(LMS_TRACKER_STORAGE_KEY);
    if (storedLMSs) {
      setTrackedLMSs(JSON.parse(storedLMSs));
    }
  }, []);

  const saveData = useCallback((data: TrackedLMS[]) => {
    localStorage.setItem(LMS_TRACKER_STORAGE_KEY, JSON.stringify(data));
  }, []);

  const openModal = (lms?: TrackedLMS) => {
    if (lms) {
      setCurrentLMS(lms);
      setLmsName(lms.name);
      setLmsNotes(lms.notes);
      setLmsStatus(lms.status);
    } else {
      setCurrentLMS(null);
      setLmsName('');
      setLmsNotes('');
      setLmsStatus(TrackedLMSStatus.RESEARCHING);
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentLMS(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!lmsName.trim()) {
      alert("LMS Name cannot be empty.");
      return;
    }

    let updatedLMSs;
    if (currentLMS) {
      updatedLMSs = trackedLMSs.map(lms =>
        lms.id === currentLMS.id ? { ...lms, name: lmsName, notes: lmsNotes, status: lmsStatus } : lms
      );
    } else {
      const newLMS: TrackedLMS = {
        id: new Date().toISOString(),
        name: lmsName,
        notes: lmsNotes,
        status: lmsStatus,
        dateAdded: new Date().toLocaleDateString(),
      };
      updatedLMSs = [...trackedLMSs, newLMS];
    }
    setTrackedLMSs(updatedLMSs);
    saveData(updatedLMSs);
    closeModal();
  };

  const handleDelete = (id: string) => {
    if (window.confirm("Are you sure you want to delete this LMS from your tracker?")) {
      const updatedLMSs = trackedLMSs.filter(lms => lms.id !== id);
      setTrackedLMSs(updatedLMSs);
      saveData(updatedLMSs);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center pb-4 border-b-2 border-primary-300">
        <h1 className="text-3xl font-semibold text-gray-800">My LMS Tracker</h1>
        <button
          onClick={() => openModal()}
          className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md flex items-center transition-colors"
        >
          <PlusIcon className="w-5 h-5 mr-2" /> Add LMS
        </button>
      </div>

      {trackedLMSs.length === 0 ? (
        <p className="text-center text-gray-500 py-10 text-lg">
          You haven't added any LMS platforms to your tracker yet. Click "Add LMS" to get started!
        </p>
      ) : (
        <div className="overflow-x-auto bg-white shadow-xl rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-100">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Added</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {trackedLMSs.map((lms) => (
                <tr key={lms.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{lms.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      lms.status === TrackedLMSStatus.ADOPTED ? 'bg-green-100 text-green-800' :
                      lms.status === TrackedLMSStatus.EVALUATING || lms.status === TrackedLMSStatus.INTERESTED ? 'bg-yellow-100 text-yellow-800' :
                      lms.status === TrackedLMSStatus.NOT_INTERESTED ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {lms.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700 max-w-xs truncate" title={lms.notes}>{lms.notes || '-'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{lms.dateAdded}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button onClick={() => openModal(lms)} className="text-primary-600 hover:text-primary-900 transition-colors" title="Edit LMS">
                      <PencilIcon />
                    </button>
                    <button onClick={() => handleDelete(lms.id)} className="text-red-600 hover:text-red-900 transition-colors" title="Delete LMS">
                      <TrashIcon />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <Modal isOpen={isModalOpen} onClose={closeModal} title={currentLMS ? 'Edit LMS' : 'Add New LMS'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="lmsName" className="block text-sm font-medium text-gray-700">LMS Name</label>
            <input
              type="text"
              id="lmsName"
              value={lmsName}
              onChange={(e) => setLmsName(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="lmsStatus" className="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="lmsStatus"
              value={lmsStatus}
              onChange={(e) => setLmsStatus(e.target.value as TrackedLMSStatus)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              {Object.values(TrackedLMSStatus).map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="lmsNotes" className="block text-sm font-medium text-gray-700">Notes</label>
            <textarea
              id="lmsNotes"
              value={lmsNotes}
              onChange={(e) => setLmsNotes(e.target.value)}
              rows={3}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-2">
            <button
              type="button"
              onClick={closeModal}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md shadow-sm transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 border border-transparent rounded-md shadow-sm transition-colors"
            >
              {currentLMS ? 'Save Changes' : 'Add LMS'}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default LMSTrackerPage;
