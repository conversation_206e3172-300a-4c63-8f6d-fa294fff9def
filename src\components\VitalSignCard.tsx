
import React from 'react';

interface VitalSignCardProps {
  label: string;
  value: string | number;
  unit: string;
  icon: React.ReactNode;
  colorClass?: string; // e.g., text-red-500 for abnormal values
  trend?: 'up' | 'down' | 'stable'; // Optional trend indicator
  description?: string; // Optional extra description
}

export const VitalSignCard: React.FC<VitalSignCardProps> = ({
  label,
  value,
  unit,
  icon,
  colorClass = 'text-blue-600',
  description,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-lg flex flex-col justify-between h-full">
      <div>
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-slate-500 uppercase tracking-wider">{label}</h3>
          <div className={`w-8 h-8 ${colorClass}`}>{icon}</div>
        </div>
        <div className="flex items-baseline">
          <span className={`text-3xl font-bold ${colorClass}`}>{value}</span>
          <span className="text-sm text-slate-600 ml-1">{unit}</span>
        </div>
      </div>
      {description && <p className="text-xs text-slate-400 mt-2">{description}</p>}
    </div>
  );
};
