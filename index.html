
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BioMed LMS - Biomedical Learning Management System</title>
  <link rel="icon" type="image/svg+xml" href="/public/favicon.svg">
  <meta name="description" content="Comprehensive biomedical learning management system for medical students and professionals">
  <meta name="keywords" content="biomedical, LMS, medical education, learning management system">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"},
            secondary: {"50":"#f0f9ff","100":"#e0f2fe","200":"#bae6fd","300":"#7dd3fc","400":"#38bdf8","500":"#0ea5e9","600":"#0284c7","700":"#0369a1","800":"#075985","900":"#0c4a6e","950":"#082f49"}
          }
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.3.1",
    "react-dom": "https://esm.sh/react-dom@18.3.1",
    "react-dom/client": "https://esm.sh/react-dom@18.3.1/client",
    "react-router-dom": "https://esm.sh/react-router-dom@6.23.1",
    "@google/genai": "https://esm.sh/@google/genai@0.12.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
</head>
<body class="bg-gray-100">
  <div id="root"></div>
  <script type="module" src="/src/index.tsx"></script>
  <link rel="stylesheet" href="/assets/css/main.css">
</body>
</html>
