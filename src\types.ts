
export interface LMSInfo {
  name: string;
  description: string;
  keyFeatures: string[];
  targetAudience: string;
}

export interface MarketPlayer {
  name: string;
  focus: string;
}

export interface GeminiLMSData {
  recommendedLMS: LMSInfo[];
  marketPlayers: MarketPlayer[];
}

export enum TrackedLMSStatus {
  RESEARCHING = 'Researching',
  EVALUATING = 'Evaluating',
  INTERESTED = 'Interested',
  NOT_INTERESTED = 'Not Interested',
  ADOPTED = 'Adopted',
}

export interface TrackedLMS {
  id: string;
  name: string;
  notes: string;
  status: TrackedLMSStatus;
  dateAdded: string;
}
